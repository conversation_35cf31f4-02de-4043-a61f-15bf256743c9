Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    main.o(i.main) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.main) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    main.o(i.main) refers to _printf_str.o(.text) for _printf_str
    main.o(i.main) refers to sys.o(i.Cache_Enable) for Cache_Enable
    main.o(i.main) refers to stm32h7xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to sys.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to usart.o(i.uart_init) for uart_init
    main.o(i.main) refers to led.o(i.LED_Init) for LED_Init
    main.o(i.main) refers to key.o(i.KEY_Init) for KEY_Init
    main.o(i.main) refers to arc.o(i.ARC_Init) for ARC_Init
    main.o(i.main) refers to noretval__2printf.o(.text) for __2printf
    main.o(i.main) refers to key.o(i.KEY_Scan) for KEY_Scan
    main.o(i.main) refers to arc.o(i.ARC_Start) for ARC_Start
    main.o(i.main) refers to arc.o(i.ARC_Stop) for ARC_Stop
    main.o(i.main) refers to strcmpv7m_pel.o(.text) for strcmp
    main.o(i.main) refers to arc.o(i.ARC_Process) for ARC_Process
    main.o(i.main) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    main.o(i.main) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to usart.o(.data) for USART_RX_STA
    main.o(i.main) refers to usart.o(.bss) for USART_RX_BUF
    main.o(i.main) refers to arc.o(.data) for ArcState
    stm32h7xx_it.o(i.SysTick_Handler) refers to stm32h7xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    system_stm32h7xx.o(i.SystemCoreClockUpdate) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    system_stm32h7xx.o(i.SystemCoreClockUpdate) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    startup_stm32h743xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h743xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h743xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h743xx.o(RESET) refers to startup_stm32h743xx.o(STACK) for __initial_sp
    startup_stm32h743xx.o(RESET) refers to startup_stm32h743xx.o(.text) for Reset_Handler
    startup_stm32h743xx.o(RESET) refers to stm32h7xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32h743xx.o(RESET) refers to stm32h7xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32h743xx.o(RESET) refers to stm32h7xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32h743xx.o(RESET) refers to stm32h7xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32h743xx.o(RESET) refers to stm32h7xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32h743xx.o(RESET) refers to stm32h7xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32h743xx.o(RESET) refers to stm32h7xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32h743xx.o(RESET) refers to stm32h7xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32h743xx.o(RESET) refers to stm32h7xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32h743xx.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32h743xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h743xx.o(.text) refers to system_stm32h7xx.o(i.SystemInit) for SystemInit
    startup_stm32h743xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32h743xx.o(.text) refers to startup_stm32h743xx.o(HEAP) for Heap_Mem
    startup_stm32h743xx.o(.text) refers to startup_stm32h743xx.o(STACK) for Stack_Mem
    delay.o(i.delay_init) refers to stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig) for HAL_SYSTICK_CLKSourceConfig
    delay.o(i.delay_init) refers to delay.o(.data) for fac_us
    delay.o(i.delay_ms) refers to delay.o(i.delay_us) for delay_us
    delay.o(i.delay_us) refers to delay.o(.data) for fac_us
    sys.o(i.Cache_Enable) refers to sys.o(i.SCB_EnableICache) for SCB_EnableICache
    sys.o(i.Cache_Enable) refers to sys.o(i.SCB_EnableDCache) for SCB_EnableDCache
    sys.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    sys.o(i.SystemClock_Config) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    sys.o(i.SystemClock_Config) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    usart.o(i.HAL_UART_MspInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.data) for USART_RX_STA
    usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for USART_RX_BUF
    usart.o(i.USART1_IRQHandler) refers to stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    usart.o(i.USART1_IRQHandler) refers to stm32h7xx_hal_uart.o(i.HAL_UART_GetState) for HAL_UART_GetState
    usart.o(i.USART1_IRQHandler) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for UART1_Handler
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for aRxBuffer
    usart.o(i.uart_init) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.uart_init) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    usart.o(i.uart_init) refers to usart.o(.bss) for UART1_Handler
    usart.o(i.uart_init) refers to usart.o(.data) for aRxBuffer
    stm32h7xx_hal.o(i.HAL_DeInit) refers to stm32h7xx_hal_msp.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32h7xx_hal.o(i.HAL_Delay) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal.o(i.HAL_Delay) refers to stm32h7xx_hal.o(.data) for uwTickFreq
    stm32h7xx_hal.o(i.HAL_GetTick) refers to stm32h7xx_hal.o(.data) for uwTick
    stm32h7xx_hal.o(i.HAL_GetTickFreq) refers to stm32h7xx_hal.o(.data) for uwTickFreq
    stm32h7xx_hal.o(i.HAL_GetTickPrio) refers to stm32h7xx_hal.o(.data) for uwTickPrio
    stm32h7xx_hal.o(i.HAL_IncTick) refers to stm32h7xx_hal.o(.data) for uwTick
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32h7xx_hal.o(i.HAL_InitTick) refers to stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32h7xx_hal.o(i.HAL_InitTick) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32h7xx_hal.o(i.HAL_InitTick) refers to stm32h7xx_hal.o(.data) for uwTickFreq
    stm32h7xx_hal.o(i.HAL_InitTick) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal.o(i.HAL_SetTickFreq) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal.o(i.HAL_SetTickFreq) refers to stm32h7xx_hal.o(.data) for uwTickFreq
    stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping) refers to stm32h7xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32h7xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32h7xx_hal_dma.o(.constdata) for flagBitshiftOffset
    stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32h7xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32h7xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32h7xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32h7xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_IRQHandler) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Init) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Init) refers to stm32h7xx_hal_mdma.o(i.MDMA_Init) for MDMA_Init
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_PollForTransfer) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_PollForTransfer) refers to stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort) for HAL_MDMA_Abort
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start) refers to stm32h7xx_hal_mdma.o(i.MDMA_SetConfig) for MDMA_SetConfig
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start_IT) refers to stm32h7xx_hal_mdma.o(i.MDMA_SetConfig) for MDMA_SetConfig
    stm32h7xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32h7xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUSBReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUSBReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_AVD_IRQHandler) refers to stm32h7xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_AVD_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_AVDCallback) for HAL_PWREx_AVDCallback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP1_Callback) for HAL_PWREx_WKUP1_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP2_Callback) for HAL_PWREx_WKUP2_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP3_Callback) for HAL_PWREx_WKUP3_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP4_Callback) for HAL_PWREx_WKUP4_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP5_Callback) for HAL_PWREx_WKUP5_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP6_Callback) for HAL_PWREx_WKUP6_Callback
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq) for HAL_RCCEx_GetD1SysClockFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32h7xx.o(.data) for SystemD2Clock
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32h7xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback) for HAL_RCCEx_CRS_SyncOkCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback) for HAL_RCCEx_CRS_SyncWarnCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback) for HAL_RCCEx_CRS_ExpectedSyncCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback) for HAL_RCCEx_CRS_ErrorCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1PCLK1Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1PCLK1Freq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL1ClockFreq) for HAL_RCCEx_GetPLL1ClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq) for HAL_RCCEx_GetPLL2ClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq) for HAL_RCCEx_GetPLL3ClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL2_Config) for RCCEx_PLL2_Config
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL3_Config) for RCCEx_PLL3_Config
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL2_Config) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL3_Config) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32h7xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32h7xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32h7xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32h7xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32h7xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32h7xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32h7xx_hal_uart.o(i.HAL_UART_DeInit) refers to stm32h7xx_hal_uart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback) for HAL_UARTEx_WakeupCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32h7xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart.o(i.HAL_UART_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart.o(i.HAL_UART_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart.o(i.HAL_UART_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32h7xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.UART_DMAError) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_uart.o(i.UART_DMAError) refers to stm32h7xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32h7xx_hal_uart.o(i.UART_DMAError) refers to stm32h7xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32h7xx_hal_uart.o(i.UART_DMAError) refers to stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32h7xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32h7xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32h7xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32h7xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32h7xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_Receive_IT) refers to usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) for HAL_RCCEx_GetD3PCLK1Freq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq) for HAL_RCCEx_GetPLL2ClockFreq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq) for HAL_RCCEx_GetPLL3ClockFreq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_uart.o(.constdata) for UARTPrescTable
    stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32h7xx_hal_uart_ex.o(i.UARTEx_Wakeup_AddressConfig) for UARTEx_Wakeup_AddressConfig
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_usart.o(i.HAL_USART_Abort) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32h7xx_hal_usart.o(i.HAL_USART_AbortCpltCallback) for HAL_USART_AbortCpltCallback
    stm32h7xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32h7xx_hal_usart.o(i.USART_DMATxAbortCallback) for USART_DMATxAbortCallback
    stm32h7xx_hal_usart.o(i.HAL_USART_Abort_IT) refers to stm32h7xx_hal_usart.o(i.USART_DMARxAbortCallback) for USART_DMARxAbortCallback
    stm32h7xx_hal_usart.o(i.HAL_USART_DMAStop) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_usart.o(i.HAL_USART_DMAStop) refers to stm32h7xx_hal_usart.o(i.USART_EndTransfer) for USART_EndTransfer
    stm32h7xx_hal_usart.o(i.HAL_USART_DeInit) refers to stm32h7xx_hal_usart.o(i.HAL_USART_MspDeInit) for HAL_USART_MspDeInit
    stm32h7xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32h7xx_hal_usart.o(i.USART_Receive_IT) for USART_Receive_IT
    stm32h7xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32h7xx_hal_usart.o(i.USART_TransmitReceive_IT) for USART_TransmitReceive_IT
    stm32h7xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32h7xx_hal_usart.o(i.USART_EndTransfer) for USART_EndTransfer
    stm32h7xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32h7xx_hal_usart.o(i.HAL_USART_ErrorCallback) for HAL_USART_ErrorCallback
    stm32h7xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32h7xx_hal_usart.o(i.USART_Transmit_IT) for USART_Transmit_IT
    stm32h7xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32h7xx_hal_usart.o(i.USART_EndTransmit_IT) for USART_EndTransmit_IT
    stm32h7xx_hal_usart.o(i.HAL_USART_IRQHandler) refers to stm32h7xx_hal_usart.o(i.USART_DMAAbortOnError) for USART_DMAAbortOnError
    stm32h7xx_hal_usart.o(i.HAL_USART_Init) refers to stm32h7xx_hal_usart.o(i.HAL_USART_MspInit) for HAL_USART_MspInit
    stm32h7xx_hal_usart.o(i.HAL_USART_Init) refers to stm32h7xx_hal_usart.o(i.USART_SetConfig) for USART_SetConfig
    stm32h7xx_hal_usart.o(i.HAL_USART_Init) refers to stm32h7xx_hal_usart.o(i.USART_CheckIdleState) for USART_CheckIdleState
    stm32h7xx_hal_usart.o(i.HAL_USART_Receive) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_usart.o(i.HAL_USART_Receive) refers to stm32h7xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32h7xx_hal_usart.o(i.USART_DMAReceiveCplt) for USART_DMAReceiveCplt
    stm32h7xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32h7xx_hal_usart.o(i.USART_DMARxHalfCplt) for USART_DMARxHalfCplt
    stm32h7xx_hal_usart.o(i.HAL_USART_Receive_DMA) refers to stm32h7xx_hal_usart.o(i.USART_DMAError) for USART_DMAError
    stm32h7xx_hal_usart.o(i.HAL_USART_Transmit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_usart.o(i.HAL_USART_Transmit) refers to stm32h7xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_usart.o(i.HAL_USART_TransmitReceive) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_usart.o(i.HAL_USART_TransmitReceive) refers to stm32h7xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32h7xx_hal_usart.o(i.USART_DMAReceiveCplt) for USART_DMAReceiveCplt
    stm32h7xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32h7xx_hal_usart.o(i.USART_DMARxHalfCplt) for USART_DMARxHalfCplt
    stm32h7xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32h7xx_hal_usart.o(i.USART_DMATransmitCplt) for USART_DMATransmitCplt
    stm32h7xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32h7xx_hal_usart.o(i.USART_DMATxHalfCplt) for USART_DMATxHalfCplt
    stm32h7xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA) refers to stm32h7xx_hal_usart.o(i.USART_DMAError) for USART_DMAError
    stm32h7xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32h7xx_hal_usart.o(i.USART_DMATransmitCplt) for USART_DMATransmitCplt
    stm32h7xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32h7xx_hal_usart.o(i.USART_DMATxHalfCplt) for USART_DMATxHalfCplt
    stm32h7xx_hal_usart.o(i.HAL_USART_Transmit_DMA) refers to stm32h7xx_hal_usart.o(i.USART_DMAError) for USART_DMAError
    stm32h7xx_hal_usart.o(i.USART_CheckIdleState) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_usart.o(i.USART_CheckIdleState) refers to stm32h7xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) for USART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_usart.o(i.USART_DMAAbortOnError) refers to stm32h7xx_hal_usart.o(i.HAL_USART_ErrorCallback) for HAL_USART_ErrorCallback
    stm32h7xx_hal_usart.o(i.USART_DMAError) refers to stm32h7xx_hal_usart.o(i.USART_EndTransfer) for USART_EndTransfer
    stm32h7xx_hal_usart.o(i.USART_DMAError) refers to stm32h7xx_hal_usart.o(i.HAL_USART_ErrorCallback) for HAL_USART_ErrorCallback
    stm32h7xx_hal_usart.o(i.USART_DMAReceiveCplt) refers to stm32h7xx_hal_usart.o(i.HAL_USART_RxCpltCallback) for HAL_USART_RxCpltCallback
    stm32h7xx_hal_usart.o(i.USART_DMAReceiveCplt) refers to stm32h7xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback) for HAL_USART_TxRxCpltCallback
    stm32h7xx_hal_usart.o(i.USART_DMARxAbortCallback) refers to stm32h7xx_hal_usart.o(i.HAL_USART_AbortCpltCallback) for HAL_USART_AbortCpltCallback
    stm32h7xx_hal_usart.o(i.USART_DMARxHalfCplt) refers to stm32h7xx_hal_usart.o(i.HAL_USART_RxHalfCpltCallback) for HAL_USART_RxHalfCpltCallback
    stm32h7xx_hal_usart.o(i.USART_DMATransmitCplt) refers to stm32h7xx_hal_usart.o(i.HAL_USART_TxCpltCallback) for HAL_USART_TxCpltCallback
    stm32h7xx_hal_usart.o(i.USART_DMATxAbortCallback) refers to stm32h7xx_hal_usart.o(i.HAL_USART_AbortCpltCallback) for HAL_USART_AbortCpltCallback
    stm32h7xx_hal_usart.o(i.USART_DMATxHalfCplt) refers to stm32h7xx_hal_usart.o(i.HAL_USART_TxHalfCpltCallback) for HAL_USART_TxHalfCpltCallback
    stm32h7xx_hal_usart.o(i.USART_EndTransmit_IT) refers to stm32h7xx_hal_usart.o(i.HAL_USART_TxCpltCallback) for HAL_USART_TxCpltCallback
    stm32h7xx_hal_usart.o(i.USART_Receive_IT) refers to stm32h7xx_hal_usart.o(i.HAL_USART_RxCpltCallback) for HAL_USART_RxCpltCallback
    stm32h7xx_hal_usart.o(i.USART_SetConfig) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32h7xx_hal_usart.o(i.USART_SetConfig) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32h7xx_hal_usart.o(i.USART_SetConfig) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq) for HAL_RCCEx_GetPLL2ClockFreq
    stm32h7xx_hal_usart.o(i.USART_SetConfig) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq) for HAL_RCCEx_GetPLL3ClockFreq
    stm32h7xx_hal_usart.o(i.USART_SetConfig) refers to stm32h7xx_hal_usart.o(.constdata) for USARTPrescTable
    stm32h7xx_hal_usart.o(i.USART_TransmitReceive_IT) refers to stm32h7xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback) for HAL_USART_TxRxCpltCallback
    stm32h7xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc.o(i.ADC_ConversionStop) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32h7xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32h7xx_hal_adc.o(i.ADC_DMAError) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32h7xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32h7xx_hal_adc.o(i.ADC_Disable) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc.o(i.ADC_Enable) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc.o(i.HAL_ADC_DeInit) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback) for HAL_ADCEx_EndOfSamplingCallback
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback) for HAL_ADCEx_LevelOutOfWindow2Callback
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback) for HAL_ADCEx_LevelOutOfWindow3Callback
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedQueueOverflowCallback) for HAL_ADCEx_InjectedQueueOverflowCallback
    stm32h7xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32h7xx_hal_adc.o(i.HAL_ADC_Init) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32h7xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc.o(i.HAL_ADC_Stop) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_IT) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_GetValue) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_SetValue) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_Enable) for ADC_Enable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32h7xx_hal_adc.o(i.ADC_ConversionStop) for ADC_ConversionStop
    stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT) refers to stm32h7xx_hal_adc.o(i.ADC_Disable) for ADC_Disable
    led.o(i.LED_Init) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    led.o(i.LED_Init) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    key.o(i.KEY_Init) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    key.o(i.KEY_Scan) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key.o(i.KEY_Scan) refers to delay.o(i.delay_ms) for delay_ms
    key.o(i.KEY_Scan) refers to key.o(.data) for key_up
    adc.o(i.ADC_Init) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.ADC_Init) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.ADC_Init) refers to adc.o(i.ADC_Start) for ADC_Start
    adc.o(i.ADC_Init) refers to noretval__2printf.o(.text) for __2printf
    adc.o(i.ADC_Init) refers to adc.o(.bss) for ADC1_Handler
    adc.o(i.ADC_Start) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    adc.o(i.ADC_Start) refers to adc.o(.bss) for ADC_ConvertedValue
    adc.o(i.ADC_Stop) refers to stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_DMA) for HAL_ADC_Stop_DMA
    adc.o(i.ADC_Stop) refers to adc.o(.bss) for ADC1_Handler
    adc.o(i.Get_AnalogInput) refers to adc.o(.bss) for ADC_ConvertedValue
    adc.o(i.Get_ColdCompensation) refers to adc.o(.bss) for ADC_ConvertedValue
    adc.o(i.Get_Pressure) refers to adc.o(.bss) for ADC_ConvertedValue
    adc.o(i.Get_Temperature) refers to adc.o(i.Get_ColdCompensation) for Get_ColdCompensation
    adc.o(i.Get_Temperature) refers to adc.o(.bss) for ADC_ConvertedValue
    adc.o(i.HAL_ADC_MspInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspInit) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(i.HAL_ADC_MspInit) refers to adc.o(.bss) for DMA1_Handler
    arc.o(i.ARC_CalcTempRate) refers to arc.o(.data) for PreviousSampleTemp
    arc.o(i.ARC_ControlCavityHeating) refers to relay.o(i.RELAY_Set) for RELAY_Set
    arc.o(i.ARC_ControlCavityHeating) refers to arc.o(.data) for CavityHeatingState
    arc.o(i.ARC_GetCavityTemp) refers to adc.o(i.Get_Temperature) for Get_Temperature
    arc.o(i.ARC_GetSampleTemp) refers to adc.o(i.Get_Temperature) for Get_Temperature
    arc.o(i.ARC_Init) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    arc.o(i.ARC_Init) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    arc.o(i.ARC_Init) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    arc.o(i.ARC_Init) refers to adc.o(i.ADC_Init) for ADC_Init
    arc.o(i.ARC_Init) refers to relay.o(i.RELAY_Init) for RELAY_Init
    arc.o(i.ARC_Init) refers to arc.o(i.ARC_ControlCavityHeating) for ARC_ControlCavityHeating
    arc.o(i.ARC_Init) refers to relay.o(i.RELAY_Set) for RELAY_Set
    arc.o(i.ARC_Init) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    arc.o(i.ARC_Init) refers to arc.o(i.ARC_GetSampleTemp) for ARC_GetSampleTemp
    arc.o(i.ARC_Init) refers to noretval__2printf.o(.text) for __2printf
    arc.o(i.ARC_Init) refers to arc.o(.data) for ArcState
    arc.o(i.ARC_Process) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    arc.o(i.ARC_Process) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    arc.o(i.ARC_Process) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    arc.o(i.ARC_Process) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    arc.o(i.ARC_Process) refers to arc.o(i.ARC_GetSampleTemp) for ARC_GetSampleTemp
    arc.o(i.ARC_Process) refers to arc.o(i.ARC_GetCavityTemp) for ARC_GetCavityTemp
    arc.o(i.ARC_Process) refers to arc.o(i.ARC_CalcTempRate) for ARC_CalcTempRate
    arc.o(i.ARC_Process) refers to arc.o(i.ARC_ControlCavityHeating) for ARC_ControlCavityHeating
    arc.o(i.ARC_Process) refers to relay.o(i.RELAY_Set) for RELAY_Set
    arc.o(i.ARC_Process) refers to noretval__2printf.o(.text) for __2printf
    arc.o(i.ARC_Process) refers to arc.o(i.ARC_UpdateState) for ARC_UpdateState
    arc.o(i.ARC_Process) refers to arc.o(i.ARC_ReportData) for ARC_ReportData
    arc.o(i.ARC_Process) refers to arc.o(.data) for LastSampleTime
    arc.o(i.ARC_ReportData) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    arc.o(i.ARC_ReportData) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    arc.o(i.ARC_ReportData) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    arc.o(i.ARC_ReportData) refers to _printf_dec.o(.text) for _printf_int_dec
    arc.o(i.ARC_ReportData) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    arc.o(i.ARC_ReportData) refers to _printf_pad.o(.text) for _printf_pre_padding
    arc.o(i.ARC_ReportData) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    arc.o(i.ARC_ReportData) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    arc.o(i.ARC_ReportData) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    arc.o(i.ARC_ReportData) refers to noretval__2sprintf.o(.text) for __2sprintf
    arc.o(i.ARC_ReportData) refers to strlen.o(.text) for strlen
    arc.o(i.ARC_ReportData) refers to arc.o(i.ARC_CalculateChecksum) for ARC_CalculateChecksum
    arc.o(i.ARC_ReportData) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    arc.o(i.ARC_ReportData) refers to arc.o(.data) for LastReportTime
    arc.o(i.ARC_ReportData) refers to usart.o(.bss) for UART1_Handler
    arc.o(i.ARC_SetRateThreshold) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    arc.o(i.ARC_SetRateThreshold) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    arc.o(i.ARC_SetRateThreshold) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    arc.o(i.ARC_SetRateThreshold) refers to noretval__2printf.o(.text) for __2printf
    arc.o(i.ARC_SetRateThreshold) refers to arc.o(.data) for TempRateThreshold
    arc.o(i.ARC_SetTempDiff) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    arc.o(i.ARC_SetTempDiff) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    arc.o(i.ARC_SetTempDiff) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    arc.o(i.ARC_SetTempDiff) refers to noretval__2printf.o(.text) for __2printf
    arc.o(i.ARC_SetTempDiff) refers to arc.o(.data) for TempDiffPreset
    arc.o(i.ARC_Start) refers to relay.o(i.RELAY_Set) for RELAY_Set
    arc.o(i.ARC_Start) refers to arc.o(i.ARC_ControlCavityHeating) for ARC_ControlCavityHeating
    arc.o(i.ARC_Start) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    arc.o(i.ARC_Start) refers to arc.o(i.ARC_GetSampleTemp) for ARC_GetSampleTemp
    arc.o(i.ARC_Start) refers to noretval__2printf.o(.text) for __2printf
    arc.o(i.ARC_Start) refers to arc.o(.data) for ArcState
    arc.o(i.ARC_Stop) refers to arc.o(i.ARC_ControlCavityHeating) for ARC_ControlCavityHeating
    arc.o(i.ARC_Stop) refers to relay.o(i.RELAY_Set) for RELAY_Set
    arc.o(i.ARC_Stop) refers to noretval__2printf.o(.text) for __2printf
    arc.o(i.ARC_Stop) refers to arc.o(.data) for ArcState
    arc.o(i.ARC_UpdateState) refers to arc.o(i.ARC_ControlCavityHeating) for ARC_ControlCavityHeating
    arc.o(i.ARC_UpdateState) refers to noretval__2printf.o(.text) for __2printf
    arc.o(i.ARC_UpdateState) refers to arc.o(.data) for ArcState
    relay.o(i.RELAY_Get) refers to relay.o(.data) for RELAY_State
    relay.o(i.RELAY_Init) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    relay.o(i.RELAY_Init) refers to relay.o(i.RELAY_SetAll) for RELAY_SetAll
    relay.o(i.RELAY_Set) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    relay.o(i.RELAY_Set) refers to relay.o(.data) for RELAY_State
    relay.o(i.RELAY_SetAll) refers to relay.o(i.RELAY_Set) for RELAY_Set
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(i.fputc) for fputc
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m_pel.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32h743xx.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.constdata), (48 bytes).
    Removing stm32h7xx_it.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_it.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_it.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_it.o(.constdata), (48 bytes).
    Removing system_stm32h7xx.o(.rev16_text), (4 bytes).
    Removing system_stm32h7xx.o(.revsh_text), (4 bytes).
    Removing system_stm32h7xx.o(.rrx_text), (6 bytes).
    Removing system_stm32h7xx.o(i.SystemCoreClockUpdate), (532 bytes).
    Removing stm32h7xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_msp.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_msp.o(.constdata), (48 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(.rrx_text), (6 bytes).
    Removing delay.o(.constdata), (48 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.rrx_text), (6 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing sys.o(i.Get_DCahceSta), (16 bytes).
    Removing sys.o(i.Get_ICahceSta), (16 bytes).
    Removing sys.o(.constdata), (48 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(.constdata), (48 bytes).
    Removing stm32h7xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DeInit), (108 bytes).
    Removing stm32h7xx_hal.o(i.HAL_Delay), (40 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DisableCompensationCell), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DisableDomain3DBGStandbyMode), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DisableDomain3DBGStopMode), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_D1_ClearFlag), (36 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_D1_EventInputConfig), (168 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_D3_EventInputConfig), (164 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_EdgeConfig), (144 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_GenerateSWInterrupt), (32 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EnableCompensationCell), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EnableDomain3DBGStandbyMode), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EnableDomain3DBGStopMode), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetFMCMemorySwappingConfig), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32h7xx_hal.o(i.HAL_ResumeTick), (18 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_AnalogSwitchConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_CM7BootAddConfig), (64 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_CompensationCodeConfig), (24 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_CompensationCodeSelect), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_DisableBOOST), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_DisableIOSpeedOptimize), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_DisableVREFBUF), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_ETHInterfaceSelect), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_EnableBOOST), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_EnableIOSpeedOptimize), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF), (60 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_VREFBUF_HighImpedanceConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_VREFBUF_TrimmingConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_VREFBUF_VoltageScalingConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SetFMCMemorySwappingConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SetTickFreq), (40 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SuspendTick), (18 bytes).
    Removing stm32h7xx_hal.o(.constdata), (48 bytes).
    Removing stm32h7xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_GetCurrentCPUID), (4 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (92 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_MPU_Disable), (44 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_MPU_Enable), (64 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (36 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (68 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetActive), (48 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (48 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (148 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (36 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (68 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32h7xx_hal_cortex.o(.constdata), (48 bytes).
    Removing stm32h7xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_Abort), (328 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit), (288 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_GetError), (6 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_GetState), (8 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_IRQHandler), (1100 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (696 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (100 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_Start), (196 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (130 bytes).
    Removing stm32h7xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (32 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (18 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxRequestGenerator), (148 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxSync), (204 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_DisableMuxRequestGenerator), (32 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_EnableMuxRequestGenerator), (32 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MUX_IRQHandler), (102 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (236 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (316 bytes).
    Removing stm32h7xx_hal_dma_ex.o(.constdata), (48 bytes).
    Removing stm32h7xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_DeInit), (388 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (28 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_LockPin), (46 bytes).
    Removing stm32h7xx_hal_gpio.o(.constdata), (48 bytes).
    Removing stm32h7xx_hal_mdma.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_mdma.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_mdma.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort), (144 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort_IT), (46 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_ConfigPostRequestMask), (110 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_DeInit), (110 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_GenerateSWRequest), (72 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_GetError), (6 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_GetState), (8 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_IRQHandler), (480 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Init), (108 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_AddNode), (230 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_CreateNode), (324 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_DisableCircularMode), (88 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_EnableCircularMode), (88 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_RemoveNode), (218 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_PollForTransfer), (292 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_RegisterCallback), (100 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start), (144 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start_IT), (204 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_UnRegisterCallback), (130 bytes).
    Removing stm32h7xx_hal_mdma.o(i.MDMA_Init), (240 bytes).
    Removing stm32h7xx_hal_mdma.o(i.MDMA_SetConfig), (152 bytes).
    Removing stm32h7xx_hal_mdma.o(.constdata), (48 bytes).
    Removing stm32h7xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (180 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DeInit), (2 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (20 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (20 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (20 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (20 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (20 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (20 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (20 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (20 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (24 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (60 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (136 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (40 bytes).
    Removing stm32h7xx_hal_pwr.o(.constdata), (48 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_AVDCallback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ClearWakeupFlag), (32 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigAVD), (180 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigD3Domain), (20 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply), (80 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ControlStopModeVoltageScaling), (24 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (68 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableAVD), (20 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBatteryCharging), (20 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (20 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableMonitoring), (20 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUSBReg), (60 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUSBVoltageDetector), (20 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableWakeUpPin), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableAVD), (20 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBatteryCharging), (32 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (64 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (20 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableMonitoring), (20 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUSBReg), (60 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUSBVoltageDetector), (20 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableWakeUpPin), (132 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTANDBYMode), (76 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOPMode), (156 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetStopModeVoltageRange), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetSupplyConfig), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetTemperatureLevel), (40 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetVBATLevel), (40 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetWakeupFlag), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_AVD_IRQHandler), (92 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler), (188 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP1_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP2_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP3_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP4_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP5_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP6_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(.constdata), (48 bytes).
    Removing stm32h7xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit), (112 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (20 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (108 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (460 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (188 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (36 bytes).
    Removing stm32h7xx_hal_rcc.o(.constdata), (48 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig), (116 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSGetSynchronizationInfo), (80 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSSoftwareSynchronizationGenerate), (20 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization), (212 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler), (156 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSECSS), (36 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS), (20 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1PCLK1Freq), (44 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL1ClockFreq), (548 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (752 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (652 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_KerWakeUpStopCLKConfig), (20 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (2872 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_WWDGxSysResetConfig), (16 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_WakeUpStopCLKConfig), (20 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL2_Config), (304 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL3_Config), (304 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(.constdata), (48 bytes).
    Removing stm32h7xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (74 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (74 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init), (124 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_LIN_Init), (160 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_LIN_SendBreak), (62 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode), (58 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode), (58 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (14 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init), (148 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Abort), (140 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive), (92 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (140 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit), (82 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (124 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT), (228 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DMAPause), (122 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DMAResume), (108 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop), (100 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DeInit), (72 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_GetError), (6 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Receive), (274 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Receive_DMA), (168 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (156 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_IT), (114 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMAError), (92 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt), (70 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMARxAbortCallback), (60 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt), (14 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (32 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMATransmitCplt), (54 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMATxAbortCallback), (60 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMATxHalfCplt), (14 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (26 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_EndTxTransfer), (20 bytes).
    Removing stm32h7xx_hal_uart_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_uart_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_uart_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set), (64 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init), (156 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_DisableStopMode), (62 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_EnableStopMode), (62 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig), (282 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.UARTEx_Wakeup_AddressConfig), (38 bytes).
    Removing stm32h7xx_hal_uart_ex.o(.constdata), (48 bytes).
    Removing stm32h7xx_hal_usart.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_usart.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_usart.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_Abort), (136 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_AbortCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_Abort_IT), (228 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_DMAPause), (154 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_DMAResume), (130 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_DMAStop), (64 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_DeInit), (56 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_ErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_GetError), (6 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_GetState), (8 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_IRQHandler), (504 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_Init), (118 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_MspInit), (2 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_Receive), (304 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_Receive_DMA), (224 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_Receive_IT), (254 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_RxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_RxHalfCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_Transmit), (198 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_TransmitReceive), (520 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_TransmitReceive_DMA), (240 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_TransmitReceive_IT), (232 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_Transmit_DMA), (152 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_Transmit_IT), (114 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_TxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_TxHalfCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_usart.o(i.HAL_USART_TxRxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_usart.o(i.USART_CheckIdleState), (112 bytes).
    Removing stm32h7xx_hal_usart.o(i.USART_DMAAbortOnError), (24 bytes).
    Removing stm32h7xx_hal_usart.o(i.USART_DMAError), (44 bytes).
    Removing stm32h7xx_hal_usart.o(i.USART_DMAReceiveCplt), (122 bytes).
    Removing stm32h7xx_hal_usart.o(i.USART_DMARxAbortCallback), (58 bytes).
    Removing stm32h7xx_hal_usart.o(i.USART_DMARxHalfCplt), (14 bytes).
    Removing stm32h7xx_hal_usart.o(i.USART_DMATransmitCplt), (70 bytes).
    Removing stm32h7xx_hal_usart.o(i.USART_DMATxAbortCallback), (58 bytes).
    Removing stm32h7xx_hal_usart.o(i.USART_DMATxHalfCplt), (14 bytes).
    Removing stm32h7xx_hal_usart.o(i.USART_EndTransfer), (36 bytes).
    Removing stm32h7xx_hal_usart.o(i.USART_EndTransmit_IT), (44 bytes).
    Removing stm32h7xx_hal_usart.o(i.USART_Receive_IT), (122 bytes).
    Removing stm32h7xx_hal_usart.o(i.USART_SetConfig), (872 bytes).
    Removing stm32h7xx_hal_usart.o(i.USART_TransmitReceive_IT), (240 bytes).
    Removing stm32h7xx_hal_usart.o(i.USART_Transmit_IT), (118 bytes).
    Removing stm32h7xx_hal_usart.o(i.USART_WaitOnFlagUntilTimeout), (104 bytes).
    Removing stm32h7xx_hal_usart.o(.constdata), (48 bytes).
    Removing stm32h7xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_adc.o(i.ADC_ConversionStop), (260 bytes).
    Removing stm32h7xx_hal_adc.o(i.ADC_Disable), (152 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (632 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_DeInit), (748 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_GetError), (6 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_GetState), (6 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_GetValue), (8 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_IRQHandler), (1020 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_PollForConversion), (428 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_PollForEvent), (242 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_Start), (348 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_Start_IT), (532 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_Stop), (76 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_DMA), (128 bytes).
    Removing stm32h7xx_hal_adc.o(i.HAL_ADC_Stop_IT), (88 bytes).
    Removing stm32h7xx_hal_adc.o(.constdata), (48 bytes).
    Removing stm32h7xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_GetValue), (44 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_SetValue), (180 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_Calibration_Start), (188 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_DisableInjectedQueue), (40 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_DisableVoltageRegulator), (52 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_EnableInjectedQueue), (48 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_EndOfSamplingCallback), (2 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_EnterADCDeepPowerDownMode), (52 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (2116 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (58 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (424 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedQueueOverflowCallback), (2 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (320 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (404 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (96 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (108 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow2Callback), (2 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LevelOutOfWindow3Callback), (2 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_GetValue), (90 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_LinearCalibration_SetValue), (204 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel), (324 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (12 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (256 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (256 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularMultiModeStop_DMA), (268 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop), (104 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_DMA), (156 bytes).
    Removing stm32h7xx_hal_adc_ex.o(i.HAL_ADCEx_RegularStop_IT), (116 bytes).
    Removing stm32h7xx_hal_adc_ex.o(.constdata), (48 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(.rrx_text), (6 bytes).
    Removing led.o(.constdata), (48 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing key.o(.constdata), (48 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.ADC_Stop), (16 bytes).
    Removing adc.o(i.Get_AnalogInput), (68 bytes).
    Removing adc.o(i.Get_Pressure), (76 bytes).
    Removing adc.o(.constdata), (48 bytes).
    Removing arc.o(.rev16_text), (4 bytes).
    Removing arc.o(.revsh_text), (4 bytes).
    Removing arc.o(.rrx_text), (6 bytes).
    Removing arc.o(i.ARC_SetRateThreshold), (108 bytes).
    Removing arc.o(i.ARC_SetTempDiff), (100 bytes).
    Removing arc.o(.constdata), (48 bytes).
    Removing relay.o(.rev16_text), (4 bytes).
    Removing relay.o(.revsh_text), (4 bytes).
    Removing relay.o(.rrx_text), (6 bytes).
    Removing relay.o(i.RELAY_Get), (28 bytes).
    Removing relay.o(.constdata), (48 bytes).

441 unused section(s) (total 41962 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m_pel.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ..\CORE\startup_stm32h743xx.s            0x00000000   Number         0  startup_stm32h743xx.o ABSOLUTE
    ..\HALLIB\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal.c 0x00000000   Number         0  stm32h7xx_hal.o ABSOLUTE
    ..\HALLIB\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_adc.c 0x00000000   Number         0  stm32h7xx_hal_adc.o ABSOLUTE
    ..\HALLIB\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_adc_ex.c 0x00000000   Number         0  stm32h7xx_hal_adc_ex.o ABSOLUTE
    ..\HALLIB\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_cortex.c 0x00000000   Number         0  stm32h7xx_hal_cortex.o ABSOLUTE
    ..\HALLIB\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dma.c 0x00000000   Number         0  stm32h7xx_hal_dma.o ABSOLUTE
    ..\HALLIB\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dma_ex.c 0x00000000   Number         0  stm32h7xx_hal_dma_ex.o ABSOLUTE
    ..\HALLIB\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_gpio.c 0x00000000   Number         0  stm32h7xx_hal_gpio.o ABSOLUTE
    ..\HALLIB\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_mdma.c 0x00000000   Number         0  stm32h7xx_hal_mdma.o ABSOLUTE
    ..\HALLIB\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_pwr.c 0x00000000   Number         0  stm32h7xx_hal_pwr.o ABSOLUTE
    ..\HALLIB\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_pwr_ex.c 0x00000000   Number         0  stm32h7xx_hal_pwr_ex.o ABSOLUTE
    ..\HALLIB\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_rcc.c 0x00000000   Number         0  stm32h7xx_hal_rcc.o ABSOLUTE
    ..\HALLIB\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_rcc_ex.c 0x00000000   Number         0  stm32h7xx_hal_rcc_ex.o ABSOLUTE
    ..\HALLIB\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_uart.c 0x00000000   Number         0  stm32h7xx_hal_uart.o ABSOLUTE
    ..\HALLIB\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_uart_ex.c 0x00000000   Number         0  stm32h7xx_hal_uart_ex.o ABSOLUTE
    ..\HALLIB\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_usart.c 0x00000000   Number         0  stm32h7xx_hal_usart.o ABSOLUTE
    ..\HARDWARE\ADC\adc.c                    0x00000000   Number         0  adc.o ABSOLUTE
    ..\HARDWARE\ARC\arc.c                    0x00000000   Number         0  arc.o ABSOLUTE
    ..\HARDWARE\KEY\key.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\RELAY\relay.c                0x00000000   Number         0  relay.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\HALLIB\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal.c 0x00000000   Number         0  stm32h7xx_hal.o ABSOLUTE
    ..\\HALLIB\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_adc.c 0x00000000   Number         0  stm32h7xx_hal_adc.o ABSOLUTE
    ..\\HALLIB\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_adc_ex.c 0x00000000   Number         0  stm32h7xx_hal_adc_ex.o ABSOLUTE
    ..\\HALLIB\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_cortex.c 0x00000000   Number         0  stm32h7xx_hal_cortex.o ABSOLUTE
    ..\\HALLIB\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_dma.c 0x00000000   Number         0  stm32h7xx_hal_dma.o ABSOLUTE
    ..\\HALLIB\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_dma_ex.c 0x00000000   Number         0  stm32h7xx_hal_dma_ex.o ABSOLUTE
    ..\\HALLIB\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_gpio.c 0x00000000   Number         0  stm32h7xx_hal_gpio.o ABSOLUTE
    ..\\HALLIB\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_mdma.c 0x00000000   Number         0  stm32h7xx_hal_mdma.o ABSOLUTE
    ..\\HALLIB\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_pwr.c 0x00000000   Number         0  stm32h7xx_hal_pwr.o ABSOLUTE
    ..\\HALLIB\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_pwr_ex.c 0x00000000   Number         0  stm32h7xx_hal_pwr_ex.o ABSOLUTE
    ..\\HALLIB\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_rcc.c 0x00000000   Number         0  stm32h7xx_hal_rcc.o ABSOLUTE
    ..\\HALLIB\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_rcc_ex.c 0x00000000   Number         0  stm32h7xx_hal_rcc_ex.o ABSOLUTE
    ..\\HALLIB\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_uart.c 0x00000000   Number         0  stm32h7xx_hal_uart.o ABSOLUTE
    ..\\HALLIB\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_uart_ex.c 0x00000000   Number         0  stm32h7xx_hal_uart_ex.o ABSOLUTE
    ..\\HALLIB\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_usart.c 0x00000000   Number         0  stm32h7xx_hal_usart.o ABSOLUTE
    ..\\HARDWARE\\ADC\\adc.c                 0x00000000   Number         0  adc.o ABSOLUTE
    ..\\HARDWARE\\ARC\\arc.c                 0x00000000   Number         0  arc.o ABSOLUTE
    ..\\HARDWARE\\KEY\\key.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\\HARDWARE\\LED\\led.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\\HARDWARE\\RELAY\\relay.c             0x00000000   Number         0  relay.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32h7xx_hal_msp.c                      0x00000000   Number         0  stm32h7xx_hal_msp.o ABSOLUTE
    stm32h7xx_hal_msp.c                      0x00000000   Number         0  stm32h7xx_hal_msp.o ABSOLUTE
    stm32h7xx_it.c                           0x00000000   Number         0  stm32h7xx_it.o ABSOLUTE
    stm32h7xx_it.c                           0x00000000   Number         0  stm32h7xx_it.o ABSOLUTE
    system_stm32h7xx.c                       0x00000000   Number         0  system_stm32h7xx.o ABSOLUTE
    system_stm32h7xx.c                       0x00000000   Number         0  system_stm32h7xx.o ABSOLUTE
    RESET                                    0x08000000   Section      664  startup_stm32h743xx.o(RESET)
    !!!main                                  0x08000298   Section        8  __main.o(!!!main)
    !!!scatter                               0x080002a0   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080002d4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080002f0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x0800030c   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x0800030c   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000009  0x08000312   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000318   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$00000014  0x0800031e   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000017  0x08000324   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000328   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800032a   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x0800032e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x0800032e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x0800032e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800032e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x0800032e   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000334   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000334   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000334   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000334   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x0800033e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800033e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800033e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800033e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800033e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800033e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800033e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800033e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800033e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800033e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800033e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800033e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800033e   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000340   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000342   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000342   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000342   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000342   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000342   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000342   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000342   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000344   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000344   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000344   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800034a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800034a   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800034e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800034e   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000356   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000358   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000358   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800035c   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000364   Section       64  startup_stm32h743xx.o(.text)
    $v0                                      0x08000364   Number         0  startup_stm32h743xx.o(.text)
    .text                                    0x080003a4   Section      238  lludivv7m.o(.text)
    .text                                    0x08000494   Section        0  noretval__2printf.o(.text)
    .text                                    0x080004ac   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x080004d4   Section        0  _printf_pad.o(.text)
    .text                                    0x08000522   Section        0  _printf_str.o(.text)
    .text                                    0x08000574   Section        0  _printf_dec.o(.text)
    .text                                    0x080005ec   Section        0  _printf_hex_int.o(.text)
    .text                                    0x08000644   Section        0  __printf_flags_wp.o(.text)
    .text                                    0x0800077c   Section        0  strlen.o(.text)
    .text                                    0x080007ba   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000808   Section      104  strcmpv7m_pel.o(.text)
    .text                                    0x08000870   Section        0  heapauxi.o(.text)
    .text                                    0x08000876   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000928   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x0800092b   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000d48   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000d49   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000d78   Section        0  _sputc.o(.text)
    .text                                    0x08000d82   Section        0  _printf_char.o(.text)
    .text                                    0x08000db0   Section        0  _printf_char_file.o(.text)
    .text                                    0x08000dd4   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08000ddc   Section      138  lludiv10.o(.text)
    .text                                    0x08000e68   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08000ee8   Section        0  bigflt0.o(.text)
    .text                                    0x08000fcc   Section        0  ferror.o(.text)
    .text                                    0x08000fd4   Section        8  libspace.o(.text)
    .text                                    0x08000fdc   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001026   Section        0  exit.o(.text)
    CL$$btod_d2e                             0x08001038   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08001076   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x080010bc   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x0800111c   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08001454   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08001530   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x0800155a   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08001584   Section      580  btod.o(CL$$btod_mult_common)
    i.ADC_DMAConvCplt                        0x080017c8   Section        0  stm32h7xx_hal_adc.o(i.ADC_DMAConvCplt)
    i.ADC_DMAError                           0x08001852   Section        0  stm32h7xx_hal_adc.o(i.ADC_DMAError)
    i.ADC_DMAHalfConvCplt                    0x08001870   Section        0  stm32h7xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    i.ADC_Enable                             0x08001880   Section        0  stm32h7xx_hal_adc.o(i.ADC_Enable)
    i.ADC_Init                               0x08001920   Section        0  adc.o(i.ADC_Init)
    i.ADC_Start                              0x08001a28   Section        0  adc.o(i.ADC_Start)
    i.ARC_CalcTempRate                       0x08001a40   Section        0  arc.o(i.ARC_CalcTempRate)
    i.ARC_CalculateChecksum                  0x08001a64   Section        0  arc.o(i.ARC_CalculateChecksum)
    i.ARC_ControlCavityHeating               0x08001a80   Section        0  arc.o(i.ARC_ControlCavityHeating)
    i.ARC_GetCavityTemp                      0x08001ad4   Section        0  arc.o(i.ARC_GetCavityTemp)
    i.ARC_GetSampleTemp                      0x08001ade   Section        0  arc.o(i.ARC_GetSampleTemp)
    i.ARC_Init                               0x08001ae8   Section        0  arc.o(i.ARC_Init)
    i.ARC_Process                            0x08001bc4   Section        0  arc.o(i.ARC_Process)
    i.ARC_ReportData                         0x08001cd0   Section        0  arc.o(i.ARC_ReportData)
    i.ARC_Start                              0x08001e24   Section        0  arc.o(i.ARC_Start)
    i.ARC_Stop                               0x08001eb4   Section        0  arc.o(i.ARC_Stop)
    i.ARC_UpdateState                        0x08001ee8   Section        0  arc.o(i.ARC_UpdateState)
    i.BusFault_Handler                       0x08001fbc   Section        0  stm32h7xx_it.o(i.BusFault_Handler)
    i.Cache_Enable                           0x08001fc0   Section        0  sys.o(i.Cache_Enable)
    i.DMA_CalcBaseAndBitshift                0x08001fdc   Section        0  stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x08001fdd   Thumb Code    46  stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CalcDMAMUXChannelBaseAndMask       0x08002010   Section        0  stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask)
    DMA_CalcDMAMUXChannelBaseAndMask         0x08002011   Thumb Code   100  stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask)
    i.DMA_CalcDMAMUXRequestGenBaseAndMask    0x08002084   Section        0  stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask)
    DMA_CalcDMAMUXRequestGenBaseAndMask      0x08002085   Thumb Code    70  stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask)
    i.DMA_CheckFifoParam                     0x080020d8   Section        0  stm32h7xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x080020d9   Thumb Code   174  stm32h7xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x08002188   Section        0  stm32h7xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08002189   Thumb Code   166  stm32h7xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x0800223c   Section        0  stm32h7xx_it.o(i.DebugMon_Handler)
    i.Get_ColdCompensation                   0x08002240   Section        0  adc.o(i.Get_ColdCompensation)
    i.Get_Temperature                        0x0800227c   Section        0  adc.o(i.Get_Temperature)
    i.HAL_ADC_ConfigChannel                  0x080022dc   Section        0  stm32h7xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_ConvCpltCallback               0x080029b4   Section        0  stm32h7xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback)
    i.HAL_ADC_ConvHalfCpltCallback           0x080029b6   Section        0  stm32h7xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    i.HAL_ADC_ErrorCallback                  0x080029b8   Section        0  stm32h7xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    i.HAL_ADC_Init                           0x080029bc   Section        0  stm32h7xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_MspInit                        0x08002d14   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_Start_DMA                      0x08002e00   Section        0  stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    i.HAL_DMA_Abort_IT                       0x08002f54   Section        0  stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_Init                           0x0800304c   Section        0  stm32h7xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x0800325c   Section        0  stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_GPIO_Init                          0x080033ac   Section        0  stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x080035f8   Section        0  stm32h7xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_TogglePin                     0x08003608   Section        0  stm32h7xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    i.HAL_GPIO_WritePin                      0x08003610   Section        0  stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x0800361c   Section        0  stm32h7xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08003628   Section        0  stm32h7xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08003640   Section        0  stm32h7xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x0800365c   Section        0  stm32h7xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x080036a8   Section        0  stm32h7xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x080036aa   Section        0  stm32h7xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x080036ca   Section        0  stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08003744   Section        0  stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCCEx_GetD1SysClockFreq            0x0800376c   Section        0  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq)
    i.HAL_RCCEx_GetD3PCLK1Freq               0x080037a4   Section        0  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq)
    i.HAL_RCCEx_GetPLL2ClockFreq             0x080037d0   Section        0  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq)
    i.HAL_RCCEx_GetPLL3ClockFreq             0x080039fc   Section        0  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq)
    i.HAL_RCC_ClockConfig                    0x08003c28   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x08003e78   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x08003eb0   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08003edc   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08003f08   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x0800414c   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_CLKSourceConfig            0x08004810   Section        0  stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig)
    i.HAL_SYSTICK_Config                     0x08004838   Section        0  stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_UARTEx_WakeupCallback              0x0800486c   Section        0  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    i.HAL_UART_ErrorCallback                 0x0800486e   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_GetState                      0x08004870   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_GetState)
    i.HAL_UART_IRQHandler                    0x08004884   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08004a40   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08004ab8   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x08004b50   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08004c30   Section        0  usart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_Transmit                      0x08004cb8   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08004d7e   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08004d80   Section        0  stm32h7xx_it.o(i.HardFault_Handler)
    i.KEY_Init                               0x08004d84   Section        0  key.o(i.KEY_Init)
    i.KEY_Scan                               0x08004dc8   Section        0  key.o(i.KEY_Scan)
    i.LED_Init                               0x08004e88   Section        0  led.o(i.LED_Init)
    i.MemManage_Handler                      0x08004ee0   Section        0  stm32h7xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08004ee4   Section        0  stm32h7xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08004ee6   Section        0  stm32h7xx_it.o(i.PendSV_Handler)
    i.RELAY_Init                             0x08004ee8   Section        0  relay.o(i.RELAY_Init)
    i.RELAY_Set                              0x08004f34   Section        0  relay.o(i.RELAY_Set)
    i.RELAY_SetAll                           0x08004fdc   Section        0  relay.o(i.RELAY_SetAll)
    i.SCB_EnableDCache                       0x08004ff8   Section        0  sys.o(i.SCB_EnableDCache)
    SCB_EnableDCache                         0x08004ff9   Thumb Code   148  sys.o(i.SCB_EnableDCache)
    i.SCB_EnableICache                       0x08005094   Section        0  sys.o(i.SCB_EnableICache)
    SCB_EnableICache                         0x08005095   Thumb Code   116  sys.o(i.SCB_EnableICache)
    i.SVC_Handler                            0x08005110   Section        0  stm32h7xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08005112   Section        0  stm32h7xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x0800511c   Section        0  sys.o(i.SystemClock_Config)
    i.SystemInit                             0x080051e0   Section        0  system_stm32h7xx.o(i.SystemInit)
    i.UART_AdvFeatureConfig                  0x08005274   Section        0  stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig)
    i.UART_CheckIdleState                    0x0800535e   Section        0  stm32h7xx_hal_uart.o(i.UART_CheckIdleState)
    i.UART_DMAAbortOnError                   0x080053d2   Section        0  stm32h7xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x080053d3   Thumb Code    24  stm32h7xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_EndRxTransfer                     0x080053ea   Section        0  stm32h7xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x080053eb   Thumb Code    32  stm32h7xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x0800540a   Section        0  stm32h7xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x0800540b   Thumb Code    32  stm32h7xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_Receive_IT                        0x0800542a   Section        0  stm32h7xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x0800542b   Thumb Code   138  stm32h7xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x080054b4   Section        0  stm32h7xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Transmit_IT                       0x08005e58   Section        0  stm32h7xx_hal_uart.o(i.UART_Transmit_IT)
    UART_Transmit_IT                         0x08005e59   Thumb Code   130  stm32h7xx_hal_uart.o(i.UART_Transmit_IT)
    i.UART_WaitOnFlagUntilTimeout            0x08005eda   Section        0  stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08005f48   Section        0  usart.o(i.USART1_IRQHandler)
    i.UsageFault_Handler                     0x08005f98   Section        0  stm32h7xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08005f9c   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__NVIC_GetPriorityGrouping             0x08005fcc   Section        0  stm32h7xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    __NVIC_GetPriorityGrouping               0x08005fcd   Thumb Code    10  stm32h7xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    i.__NVIC_SetPriority                     0x08005fdc   Section        0  stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08005fdd   Thumb Code    32  stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority)
    i._is_digit                              0x08006004   Section        0  __printf_wp.o(i._is_digit)
    i._sys_exit                              0x08006012   Section        0  usart.o(i._sys_exit)
    i.delay_init                             0x08006018   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x0800602c   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x08006044   Section        0  delay.o(i.delay_us)
    i.fputc                                  0x0800608c   Section        0  usart.o(i.fputc)
    i.main                                   0x080060a8   Section        0  main.o(i.main)
    i.uart_init                              0x0800624c   Section        0  usart.o(i.uart_init)
    locale$$code                             0x08006284   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$fpinit                             0x080062b0   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x080062b0   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$printf1                            0x080062ba   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x080062ba   Number         0  printf1.o(x$fpl$printf1)
    .constdata                               0x080062be   Section       64  system_stm32h7xx.o(.constdata)
    x$fpl$usenofp                            0x080062be   Section        0  usenofp.o(x$fpl$usenofp)
    UARTPrescTable                           0x080062be   Data          24  system_stm32h7xx.o(.constdata)
    USARTPrescTable                          0x080062d6   Data          24  system_stm32h7xx.o(.constdata)
    .constdata                               0x080062fe   Section       56  stm32h7xx_hal_dma.o(.constdata)
    UARTPrescTable                           0x080062fe   Data          24  stm32h7xx_hal_dma.o(.constdata)
    USARTPrescTable                          0x08006316   Data          24  stm32h7xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x0800632e   Data           8  stm32h7xx_hal_dma.o(.constdata)
    .constdata                               0x08006336   Section       48  stm32h7xx_hal_uart.o(.constdata)
    UARTPrescTable                           0x08006336   Data          24  stm32h7xx_hal_uart.o(.constdata)
    USARTPrescTable                          0x0800634e   Data          24  stm32h7xx_hal_uart.o(.constdata)
    .constdata                               0x08006366   Section       40  _printf_hex_int.o(.constdata)
    uc_hextab                                0x08006366   Data          20  _printf_hex_int.o(.constdata)
    lc_hextab                                0x0800637a   Data          20  _printf_hex_int.o(.constdata)
    .constdata                               0x0800638e   Section       17  __printf_flags_wp.o(.constdata)
    maptable                                 0x0800638e   Data          17  __printf_flags_wp.o(.constdata)
    .constdata                               0x080063a0   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x080063a0   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x080063dc   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x08006454   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08006458   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08006460   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x0800646c   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0800646e   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0800646f   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x08006470   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x24000000   Section        8  system_stm32h7xx.o(.data)
    .data                                    0x24000008   Section        4  delay.o(.data)
    fac_us                                   0x24000008   Data           4  delay.o(.data)
    .data                                    0x2400000c   Section        7  usart.o(.data)
    .data                                    0x24000014   Section        9  stm32h7xx_hal.o(.data)
    uwTick                                   0x24000014   Data           4  stm32h7xx_hal.o(.data)
    uwTickPrio                               0x24000018   Data           4  stm32h7xx_hal.o(.data)
    uwTickFreq                               0x2400001c   Data           1  stm32h7xx_hal.o(.data)
    .data                                    0x2400001d   Section        1  key.o(.data)
    key_up                                   0x2400001d   Data           1  key.o(.data)
    .data                                    0x24000020   Section       52  arc.o(.data)
    SampleTemp                               0x24000024   Data           4  arc.o(.data)
    CavityTemp                               0x24000028   Data           4  arc.o(.data)
    TempDiff                                 0x2400002c   Data           4  arc.o(.data)
    SampleTempRate                           0x24000030   Data           4  arc.o(.data)
    LastSampleTime                           0x24000034   Data           4  arc.o(.data)
    LastRateCalcTime                         0x24000038   Data           4  arc.o(.data)
    PreviousSampleTemp                       0x2400003c   Data           4  arc.o(.data)
    CavityHeatingState                       0x24000040   Data           1  arc.o(.data)
    TempDiffPreset                           0x24000044   Data           4  arc.o(.data)
    TempRateThreshold                        0x24000048   Data           4  arc.o(.data)
    ExperimentStartTime                      0x2400004c   Data           4  arc.o(.data)
    LastReportTime                           0x24000050   Data           4  arc.o(.data)
    .data                                    0x24000054   Section        8  relay.o(.data)
    RELAY_State                              0x24000054   Data           8  relay.o(.data)
    .bss                                     0x2400005c   Section      328  usart.o(.bss)
    .bss                                     0x240001a4   Section      252  adc.o(.bss)
    .bss                                     0x240002a0   Section       96  libspace.o(.bss)
    HEAP                                     0x24000300   Section      512  startup_stm32h743xx.o(HEAP)
    Heap_Mem                                 0x24000300   Data         512  startup_stm32h743xx.o(HEAP)
    STACK                                    0x24000500   Section     1024  startup_stm32h743xx.o(STACK)
    Stack_Mem                                0x24000500   Data        1024  startup_stm32h743xx.o(STACK)
    __initial_sp                             0x24000900   Data           0  startup_stm32h743xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPv5_D16$PE$PLD8$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000298   Number         0  startup_stm32h743xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32h743xx.o(RESET)
    __Vectors_End                            0x08000298   Data           0  startup_stm32h743xx.o(RESET)
    __main                                   0x08000299   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080002a1   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080002a1   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080002a1   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x080002af   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080002d5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080002f1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x0800030d   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x0800030d   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x08000313   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_x                                0x08000319   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_s                                0x0800031f   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_percent_end                      0x08000325   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000329   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800032b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x0800032f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x0800032f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x0800032f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x0800032f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x0800032f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000335   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000335   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000335   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000335   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x0800033f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800033f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800033f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800033f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800033f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800033f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800033f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800033f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x0800033f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800033f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x0800033f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800033f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800033f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000341   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000343   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000343   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000343   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000343   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000343   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000343   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000343   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000345   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000345   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000345   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800034b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800034b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800034f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800034f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000357   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000359   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000359   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800035d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000365   Thumb Code     8  startup_stm32h743xx.o(.text)
    ADC3_IRQHandler                          0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    ADC_IRQHandler                           0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    BDMA_Channel0_IRQHandler                 0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    BDMA_Channel1_IRQHandler                 0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    BDMA_Channel2_IRQHandler                 0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    BDMA_Channel3_IRQHandler                 0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    BDMA_Channel4_IRQHandler                 0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    BDMA_Channel5_IRQHandler                 0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    BDMA_Channel6_IRQHandler                 0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    BDMA_Channel7_IRQHandler                 0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    CEC_IRQHandler                           0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    COMP1_IRQHandler                         0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    CRS_IRQHandler                           0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DCMI_IRQHandler                          0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DFSDM1_FLT0_IRQHandler                   0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DFSDM1_FLT1_IRQHandler                   0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DFSDM1_FLT2_IRQHandler                   0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DFSDM1_FLT3_IRQHandler                   0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DMA2D_IRQHandler                         0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DMAMUX1_OVR_IRQHandler                   0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    DMAMUX2_OVR_IRQHandler                   0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    ETH_IRQHandler                           0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    ETH_WKUP_IRQHandler                      0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    EXTI0_IRQHandler                         0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    EXTI15_10_IRQHandler                     0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    EXTI1_IRQHandler                         0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    EXTI2_IRQHandler                         0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    EXTI3_IRQHandler                         0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    EXTI4_IRQHandler                         0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    EXTI9_5_IRQHandler                       0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    FDCAN1_IT0_IRQHandler                    0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    FDCAN1_IT1_IRQHandler                    0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    FDCAN2_IT0_IRQHandler                    0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    FDCAN2_IT1_IRQHandler                    0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    FDCAN_CAL_IRQHandler                     0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    FLASH_IRQHandler                         0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    FMC_IRQHandler                           0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    FPU_IRQHandler                           0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    HRTIM1_FLT_IRQHandler                    0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    HRTIM1_Master_IRQHandler                 0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    HRTIM1_TIMA_IRQHandler                   0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    HRTIM1_TIMB_IRQHandler                   0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    HRTIM1_TIMC_IRQHandler                   0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    HRTIM1_TIMD_IRQHandler                   0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    HRTIM1_TIME_IRQHandler                   0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    HSEM1_IRQHandler                         0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    I2C1_ER_IRQHandler                       0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    I2C1_EV_IRQHandler                       0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    I2C2_ER_IRQHandler                       0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    I2C2_EV_IRQHandler                       0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    I2C3_ER_IRQHandler                       0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    I2C3_EV_IRQHandler                       0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    I2C4_ER_IRQHandler                       0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    I2C4_EV_IRQHandler                       0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    JPEG_IRQHandler                          0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    LPTIM1_IRQHandler                        0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    LPTIM2_IRQHandler                        0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    LPTIM3_IRQHandler                        0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    LPTIM4_IRQHandler                        0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    LPTIM5_IRQHandler                        0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    LPUART1_IRQHandler                       0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    LTDC_ER_IRQHandler                       0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    LTDC_IRQHandler                          0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    MDIOS_IRQHandler                         0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    MDIOS_WKUP_IRQHandler                    0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    MDMA_IRQHandler                          0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    OTG_FS_EP1_IN_IRQHandler                 0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    OTG_FS_EP1_OUT_IRQHandler                0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    OTG_FS_IRQHandler                        0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    OTG_HS_IRQHandler                        0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    PVD_AVD_IRQHandler                       0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    QUADSPI_IRQHandler                       0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    RCC_IRQHandler                           0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    RNG_IRQHandler                           0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    SAI1_IRQHandler                          0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    SAI2_IRQHandler                          0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    SAI3_IRQHandler                          0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    SAI4_IRQHandler                          0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    SDMMC1_IRQHandler                        0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    SDMMC2_IRQHandler                        0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    SPDIF_RX_IRQHandler                      0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    SPI1_IRQHandler                          0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    SPI2_IRQHandler                          0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    SPI3_IRQHandler                          0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    SPI4_IRQHandler                          0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    SPI5_IRQHandler                          0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    SPI6_IRQHandler                          0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    SWPMI1_IRQHandler                        0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    TIM15_IRQHandler                         0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    TIM16_IRQHandler                         0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    TIM17_IRQHandler                         0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    TIM1_BRK_IRQHandler                      0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    TIM1_CC_IRQHandler                       0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    TIM1_UP_IRQHandler                       0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    TIM2_IRQHandler                          0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    TIM3_IRQHandler                          0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    TIM4_IRQHandler                          0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    TIM5_IRQHandler                          0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    TIM7_IRQHandler                          0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    TIM8_CC_IRQHandler                       0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    UART4_IRQHandler                         0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    UART5_IRQHandler                         0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    UART7_IRQHandler                         0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    UART8_IRQHandler                         0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    USART2_IRQHandler                        0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    USART3_IRQHandler                        0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    USART6_IRQHandler                        0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    WAKEUP_PIN_IRQHandler                    0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    WWDG_IRQHandler                          0x0800037f   Thumb Code     0  startup_stm32h743xx.o(.text)
    __user_initial_stackheap                 0x08000381   Thumb Code     0  startup_stm32h743xx.o(.text)
    __aeabi_uldivmod                         0x080003a5   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x080003a5   Thumb Code   238  lludivv7m.o(.text)
    __2printf                                0x08000495   Thumb Code    20  noretval__2printf.o(.text)
    __2sprintf                               0x080004ad   Thumb Code    34  noretval__2sprintf.o(.text)
    _printf_pre_padding                      0x080004d5   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000501   Thumb Code    34  _printf_pad.o(.text)
    _printf_str                              0x08000523   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08000575   Thumb Code   104  _printf_dec.o(.text)
    _printf_int_hex                          0x080005ed   Thumb Code    84  _printf_hex_int.o(.text)
    _printf_longlong_hex                     0x080005ed   Thumb Code     0  _printf_hex_int.o(.text)
    __printf                                 0x08000645   Thumb Code   308  __printf_flags_wp.o(.text)
    strlen                                   0x0800077d   Thumb Code    62  strlen.o(.text)
    __aeabi_memclr4                          0x080007bb   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080007bb   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080007bb   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080007bf   Thumb Code     0  rt_memclr_w.o(.text)
    strcmp                                   0x08000809   Thumb Code   104  strcmpv7m_pel.o(.text)
    __use_two_region_memory                  0x08000871   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000873   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000875   Thumb Code     2  heapauxi.o(.text)
    _printf_int_common                       0x08000877   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08000929   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000adb   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08000d53   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000d79   Thumb Code    10  _sputc.o(.text)
    _printf_cs_common                        0x08000d83   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08000d97   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08000da7   Thumb Code     8  _printf_char.o(.text)
    _printf_char_file                        0x08000db1   Thumb Code    32  _printf_char_file.o(.text)
    __rt_locale                              0x08000dd5   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _ll_udiv10                               0x08000ddd   Thumb Code   138  lludiv10.o(.text)
    _printf_fp_infnan                        0x08000e69   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08000ee9   Thumb Code   224  bigflt0.o(.text)
    ferror                                   0x08000fcd   Thumb Code     8  ferror.o(.text)
    __user_libspace                          0x08000fd5   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000fd5   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000fd5   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08000fdd   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08001027   Thumb Code    18  exit.o(.text)
    _btod_d2e                                0x08001039   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08001077   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x080010bd   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x0800111d   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08001455   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08001531   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x0800155b   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08001585   Thumb Code   580  btod.o(CL$$btod_mult_common)
    ADC_DMAConvCplt                          0x080017c9   Thumb Code   138  stm32h7xx_hal_adc.o(i.ADC_DMAConvCplt)
    ADC_DMAError                             0x08001853   Thumb Code    30  stm32h7xx_hal_adc.o(i.ADC_DMAError)
    ADC_DMAHalfConvCplt                      0x08001871   Thumb Code    14  stm32h7xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    ADC_Enable                               0x08001881   Thumb Code   154  stm32h7xx_hal_adc.o(i.ADC_Enable)
    ADC_Init                                 0x08001921   Thumb Code   238  adc.o(i.ADC_Init)
    ADC_Start                                0x08001a29   Thumb Code    14  adc.o(i.ADC_Start)
    ARC_CalcTempRate                         0x08001a41   Thumb Code    32  arc.o(i.ARC_CalcTempRate)
    ARC_CalculateChecksum                    0x08001a65   Thumb Code    26  arc.o(i.ARC_CalculateChecksum)
    ARC_ControlCavityHeating                 0x08001a81   Thumb Code    78  arc.o(i.ARC_ControlCavityHeating)
    ARC_GetCavityTemp                        0x08001ad5   Thumb Code    10  arc.o(i.ARC_GetCavityTemp)
    ARC_GetSampleTemp                        0x08001adf   Thumb Code    10  arc.o(i.ARC_GetSampleTemp)
    ARC_Init                                 0x08001ae9   Thumb Code   106  arc.o(i.ARC_Init)
    ARC_Process                              0x08001bc5   Thumb Code   188  arc.o(i.ARC_Process)
    ARC_ReportData                           0x08001cd1   Thumb Code   256  arc.o(i.ARC_ReportData)
    ARC_Start                                0x08001e25   Thumb Code    86  arc.o(i.ARC_Start)
    ARC_Stop                                 0x08001eb5   Thumb Code    30  arc.o(i.ARC_Stop)
    ARC_UpdateState                          0x08001ee9   Thumb Code   112  arc.o(i.ARC_UpdateState)
    BusFault_Handler                         0x08001fbd   Thumb Code     4  stm32h7xx_it.o(i.BusFault_Handler)
    Cache_Enable                             0x08001fc1   Thumb Code    24  sys.o(i.Cache_Enable)
    DebugMon_Handler                         0x0800223d   Thumb Code     2  stm32h7xx_it.o(i.DebugMon_Handler)
    Get_ColdCompensation                     0x08002241   Thumb Code    46  adc.o(i.Get_ColdCompensation)
    Get_Temperature                          0x0800227d   Thumb Code    74  adc.o(i.Get_Temperature)
    HAL_ADC_ConfigChannel                    0x080022dd   Thumb Code  1722  stm32h7xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x080029b5   Thumb Code     2  stm32h7xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ConvHalfCpltCallback             0x080029b7   Thumb Code     2  stm32h7xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    HAL_ADC_ErrorCallback                    0x080029b9   Thumb Code     2  stm32h7xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    HAL_ADC_Init                             0x080029bd   Thumb Code   820  stm32h7xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_MspInit                          0x08002d15   Thumb Code   208  adc.o(i.HAL_ADC_MspInit)
    HAL_ADC_Start_DMA                        0x08002e01   Thumb Code   314  stm32h7xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    HAL_DMA_Abort_IT                         0x08002f55   Thumb Code   236  stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_Init                             0x0800304d   Thumb Code   512  stm32h7xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x0800325d   Thumb Code   328  stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_GPIO_Init                            0x080033ad   Thumb Code   534  stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x080035f9   Thumb Code    16  stm32h7xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_TogglePin                       0x08003609   Thumb Code     8  stm32h7xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    HAL_GPIO_WritePin                        0x08003611   Thumb Code    10  stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x0800361d   Thumb Code     6  stm32h7xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08003629   Thumb Code    16  stm32h7xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08003641   Thumb Code    28  stm32h7xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x0800365d   Thumb Code    64  stm32h7xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x080036a9   Thumb Code     2  stm32h7xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x080036ab   Thumb Code    32  stm32h7xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x080036cb   Thumb Code   122  stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08003745   Thumb Code    32  stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCCEx_GetD1SysClockFreq              0x0800376d   Thumb Code    44  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq)
    HAL_RCCEx_GetD3PCLK1Freq                 0x080037a5   Thumb Code    34  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq)
    HAL_RCCEx_GetPLL2ClockFreq               0x080037d1   Thumb Code   532  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq)
    HAL_RCCEx_GetPLL3ClockFreq               0x080039fd   Thumb Code   532  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq)
    HAL_RCC_ClockConfig                      0x08003c29   Thumb Code   574  stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x08003e79   Thumb Code    42  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08003eb1   Thumb Code    34  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08003edd   Thumb Code    36  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08003f09   Thumb Code   556  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x0800414d   Thumb Code  1724  stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_CLKSourceConfig              0x08004811   Thumb Code    40  stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig)
    HAL_SYSTICK_Config                       0x08004839   Thumb Code    52  stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_UARTEx_WakeupCallback                0x0800486d   Thumb Code     2  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    HAL_UART_ErrorCallback                   0x0800486f   Thumb Code     2  stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_GetState                        0x08004871   Thumb Code    20  stm32h7xx_hal_uart.o(i.HAL_UART_GetState)
    HAL_UART_IRQHandler                      0x08004885   Thumb Code   434  stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08004a41   Thumb Code   120  stm32h7xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08004ab9   Thumb Code   138  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x08004b51   Thumb Code   224  stm32h7xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08004c31   Thumb Code   120  usart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_Transmit                        0x08004cb9   Thumb Code   198  stm32h7xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08004d7f   Thumb Code     2  stm32h7xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08004d81   Thumb Code     4  stm32h7xx_it.o(i.HardFault_Handler)
    KEY_Init                                 0x08004d85   Thumb Code    60  key.o(i.KEY_Init)
    KEY_Scan                                 0x08004dc9   Thumb Code   184  key.o(i.KEY_Scan)
    LED_Init                                 0x08004e89   Thumb Code    78  led.o(i.LED_Init)
    MemManage_Handler                        0x08004ee1   Thumb Code     4  stm32h7xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08004ee5   Thumb Code     2  stm32h7xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08004ee7   Thumb Code     2  stm32h7xx_it.o(i.PendSV_Handler)
    RELAY_Init                               0x08004ee9   Thumb Code    68  relay.o(i.RELAY_Init)
    RELAY_Set                                0x08004f35   Thumb Code   160  relay.o(i.RELAY_Set)
    RELAY_SetAll                             0x08004fdd   Thumb Code    26  relay.o(i.RELAY_SetAll)
    SVC_Handler                              0x08005111   Thumb Code     2  stm32h7xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08005113   Thumb Code     8  stm32h7xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x0800511d   Thumb Code   190  sys.o(i.SystemClock_Config)
    SystemInit                               0x080051e1   Thumb Code   130  system_stm32h7xx.o(i.SystemInit)
    UART_AdvFeatureConfig                    0x08005275   Thumb Code   234  stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig)
    UART_CheckIdleState                      0x0800535f   Thumb Code   116  stm32h7xx_hal_uart.o(i.UART_CheckIdleState)
    UART_SetConfig                           0x080054b5   Thumb Code  2452  stm32h7xx_hal_uart.o(i.UART_SetConfig)
    UART_WaitOnFlagUntilTimeout              0x08005edb   Thumb Code   108  stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    USART1_IRQHandler                        0x08005f49   Thumb Code    66  usart.o(i.USART1_IRQHandler)
    UsageFault_Handler                       0x08005f99   Thumb Code     4  stm32h7xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08005f9d   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x08006005   Thumb Code    14  __printf_wp.o(i._is_digit)
    _sys_exit                                0x08006013   Thumb Code     4  usart.o(i._sys_exit)
    delay_init                               0x08006019   Thumb Code    16  delay.o(i.delay_init)
    delay_ms                                 0x0800602d   Thumb Code    24  delay.o(i.delay_ms)
    delay_us                                 0x08006045   Thumb Code    68  delay.o(i.delay_us)
    fputc                                    0x0800608d   Thumb Code    22  usart.o(i.fputc)
    main                                     0x080060a9   Thumb Code   270  main.o(i.main)
    uart_init                                0x0800624d   Thumb Code    44  usart.o(i.uart_init)
    _get_lc_numeric                          0x08006285   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _fp_init                                 0x080062b1   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x080062b9   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x080062b9   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    _printf_fp_dec                           0x080062bb   Thumb Code     4  printf1.o(x$fpl$printf1)
    __I$use$fp                               0x080062be   Number         0  usenofp.o(x$fpl$usenofp)
    D1CorePrescTable                         0x080062ee   Data          16  system_stm32h7xx.o(.constdata)
    Region$$Table$$Base                      0x08006434   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08006454   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x24000000   Data           4  system_stm32h7xx.o(.data)
    SystemD2Clock                            0x24000004   Data           4  system_stm32h7xx.o(.data)
    __stdout                                 0x2400000c   Data           4  usart.o(.data)
    USART_RX_STA                             0x24000010   Data           2  usart.o(.data)
    aRxBuffer                                0x24000012   Data           1  usart.o(.data)
    ArcState                                 0x24000020   Data           1  arc.o(.data)
    USART_RX_BUF                             0x2400005c   Data         200  usart.o(.bss)
    UART1_Handler                            0x24000124   Data         128  usart.o(.bss)
    ADC1_Handler                             0x240001a4   Data         100  adc.o(.bss)
    DMA1_Handler                             0x24000208   Data         120  adc.o(.bss)
    ADC_ConvertedValue                       0x24000280   Data          32  adc.o(.bss)
    __libspace_start                         0x240002a0   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x24000300   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000299

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000064cc, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00006470, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000298   Data   RO          481    RESET               startup_stm32h743xx.o
    0x08000298   0x08000298   0x00000008   Code   RO         3581  * !!!main             c_w.l(__main.o)
    0x080002a0   0x080002a0   0x00000034   Code   RO         3809    !!!scatter          c_w.l(__scatter.o)
    0x080002d4   0x080002d4   0x0000001a   Code   RO         3811    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080002ee   0x080002ee   0x00000002   PAD
    0x080002f0   0x080002f0   0x0000001c   Code   RO         3813    !!handler_zi        c_w.l(__scatter_zi.o)
    0x0800030c   0x0800030c   0x00000000   Code   RO         3572    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x0800030c   0x0800030c   0x00000006   Code   RO         3571    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000312   0x08000312   0x00000006   Code   RO         3570    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000318   0x08000318   0x00000006   Code   RO         3569    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x0800031e   0x0800031e   0x00000006   Code   RO         3568    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x08000324   0x08000324   0x00000004   Code   RO         3602    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000328   0x08000328   0x00000002   Code   RO         3684    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800032a   0x0800032a   0x00000004   Code   RO         3685    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x0800032e   0x0800032e   0x00000000   Code   RO         3688    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x0800032e   0x0800032e   0x00000000   Code   RO         3691    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x0800032e   0x0800032e   0x00000000   Code   RO         3693    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x0800032e   0x0800032e   0x00000000   Code   RO         3695    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x0800032e   0x0800032e   0x00000006   Code   RO         3696    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000334   0x08000334   0x00000000   Code   RO         3698    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000334   0x08000334   0x00000000   Code   RO         3700    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000334   0x08000334   0x00000000   Code   RO         3702    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000334   0x08000334   0x0000000a   Code   RO         3703    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x0800033e   0x0800033e   0x00000000   Code   RO         3704    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800033e   0x0800033e   0x00000000   Code   RO         3706    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800033e   0x0800033e   0x00000000   Code   RO         3708    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800033e   0x0800033e   0x00000000   Code   RO         3710    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800033e   0x0800033e   0x00000000   Code   RO         3712    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800033e   0x0800033e   0x00000000   Code   RO         3714    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800033e   0x0800033e   0x00000000   Code   RO         3716    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800033e   0x0800033e   0x00000000   Code   RO         3718    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800033e   0x0800033e   0x00000000   Code   RO         3722    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800033e   0x0800033e   0x00000000   Code   RO         3724    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800033e   0x0800033e   0x00000000   Code   RO         3726    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800033e   0x0800033e   0x00000000   Code   RO         3728    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800033e   0x0800033e   0x00000002   Code   RO         3729    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000340   0x08000340   0x00000002   Code   RO         3771    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000342   0x08000342   0x00000000   Code   RO         3792    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000342   0x08000342   0x00000000   Code   RO         3794    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000342   0x08000342   0x00000000   Code   RO         3797    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000342   0x08000342   0x00000000   Code   RO         3800    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000342   0x08000342   0x00000000   Code   RO         3802    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000342   0x08000342   0x00000000   Code   RO         3805    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000342   0x08000342   0x00000002   Code   RO         3806    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000344   0x08000344   0x00000000   Code   RO         3585    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000344   0x08000344   0x00000000   Code   RO         3604    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000344   0x08000344   0x00000006   Code   RO         3616    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800034a   0x0800034a   0x00000000   Code   RO         3606    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800034a   0x0800034a   0x00000004   Code   RO         3607    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800034e   0x0800034e   0x00000000   Code   RO         3609    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800034e   0x0800034e   0x00000008   Code   RO         3610    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000356   0x08000356   0x00000002   Code   RO         3730    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000358   0x08000358   0x00000000   Code   RO         3743    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000358   0x08000358   0x00000004   Code   RO         3744    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800035c   0x0800035c   0x00000006   Code   RO         3745    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000362   0x08000362   0x00000002   PAD
    0x08000364   0x08000364   0x00000040   Code   RO          482    .text               startup_stm32h743xx.o
    0x080003a4   0x080003a4   0x000000ee   Code   RO         3509    .text               c_w.l(lludivv7m.o)
    0x08000492   0x08000492   0x00000002   PAD
    0x08000494   0x08000494   0x00000018   Code   RO         3515    .text               c_w.l(noretval__2printf.o)
    0x080004ac   0x080004ac   0x00000028   Code   RO         3517    .text               c_w.l(noretval__2sprintf.o)
    0x080004d4   0x080004d4   0x0000004e   Code   RO         3521    .text               c_w.l(_printf_pad.o)
    0x08000522   0x08000522   0x00000052   Code   RO         3523    .text               c_w.l(_printf_str.o)
    0x08000574   0x08000574   0x00000078   Code   RO         3525    .text               c_w.l(_printf_dec.o)
    0x080005ec   0x080005ec   0x00000058   Code   RO         3530    .text               c_w.l(_printf_hex_int.o)
    0x08000644   0x08000644   0x00000138   Code   RO         3560    .text               c_w.l(__printf_flags_wp.o)
    0x0800077c   0x0800077c   0x0000003e   Code   RO         3573    .text               c_w.l(strlen.o)
    0x080007ba   0x080007ba   0x0000004e   Code   RO         3575    .text               c_w.l(rt_memclr_w.o)
    0x08000808   0x08000808   0x00000068   Code   RO         3577    .text               c_w.l(strcmpv7m_pel.o)
    0x08000870   0x08000870   0x00000006   Code   RO         3579    .text               c_w.l(heapauxi.o)
    0x08000876   0x08000876   0x000000b2   Code   RO         3590    .text               c_w.l(_printf_intcommon.o)
    0x08000928   0x08000928   0x0000041e   Code   RO         3592    .text               c_w.l(_printf_fp_dec.o)
    0x08000d46   0x08000d46   0x00000002   PAD
    0x08000d48   0x08000d48   0x00000030   Code   RO         3594    .text               c_w.l(_printf_char_common.o)
    0x08000d78   0x08000d78   0x0000000a   Code   RO         3596    .text               c_w.l(_sputc.o)
    0x08000d82   0x08000d82   0x0000002c   Code   RO         3598    .text               c_w.l(_printf_char.o)
    0x08000dae   0x08000dae   0x00000002   PAD
    0x08000db0   0x08000db0   0x00000024   Code   RO         3600    .text               c_w.l(_printf_char_file.o)
    0x08000dd4   0x08000dd4   0x00000008   Code   RO         3623    .text               c_w.l(rt_locale_intlibspace.o)
    0x08000ddc   0x08000ddc   0x0000008a   Code   RO         3625    .text               c_w.l(lludiv10.o)
    0x08000e66   0x08000e66   0x00000002   PAD
    0x08000e68   0x08000e68   0x00000080   Code   RO         3627    .text               c_w.l(_printf_fp_infnan.o)
    0x08000ee8   0x08000ee8   0x000000e4   Code   RO         3631    .text               c_w.l(bigflt0.o)
    0x08000fcc   0x08000fcc   0x00000008   Code   RO         3656    .text               c_w.l(ferror.o)
    0x08000fd4   0x08000fd4   0x00000008   Code   RO         3668    .text               c_w.l(libspace.o)
    0x08000fdc   0x08000fdc   0x0000004a   Code   RO         3671    .text               c_w.l(sys_stackheap_outer.o)
    0x08001026   0x08001026   0x00000012   Code   RO         3673    .text               c_w.l(exit.o)
    0x08001038   0x08001038   0x0000003e   Code   RO         3634    CL$$btod_d2e        c_w.l(btod.o)
    0x08001076   0x08001076   0x00000046   Code   RO         3636    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x080010bc   0x080010bc   0x00000060   Code   RO         3635    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x0800111c   0x0800111c   0x00000338   Code   RO         3644    CL$$btod_div_common  c_w.l(btod.o)
    0x08001454   0x08001454   0x000000dc   Code   RO         3641    CL$$btod_e2e        c_w.l(btod.o)
    0x08001530   0x08001530   0x0000002a   Code   RO         3638    CL$$btod_ediv       c_w.l(btod.o)
    0x0800155a   0x0800155a   0x0000002a   Code   RO         3637    CL$$btod_emul       c_w.l(btod.o)
    0x08001584   0x08001584   0x00000244   Code   RO         3643    CL$$btod_mult_common  c_w.l(btod.o)
    0x080017c8   0x080017c8   0x0000008a   Code   RO         2846    i.ADC_DMAConvCplt   stm32h7xx_hal_adc.o
    0x08001852   0x08001852   0x0000001e   Code   RO         2847    i.ADC_DMAError      stm32h7xx_hal_adc.o
    0x08001870   0x08001870   0x0000000e   Code   RO         2848    i.ADC_DMAHalfConvCplt  stm32h7xx_hal_adc.o
    0x0800187e   0x0800187e   0x00000002   PAD
    0x08001880   0x08001880   0x000000a0   Code   RO         2850    i.ADC_Enable        stm32h7xx_hal_adc.o
    0x08001920   0x08001920   0x00000108   Code   RO         3286    i.ADC_Init          adc.o
    0x08001a28   0x08001a28   0x00000018   Code   RO         3287    i.ADC_Start         adc.o
    0x08001a40   0x08001a40   0x00000024   Code   RO         3359    i.ARC_CalcTempRate  arc.o
    0x08001a64   0x08001a64   0x0000001a   Code   RO         3360    i.ARC_CalculateChecksum  arc.o
    0x08001a7e   0x08001a7e   0x00000002   PAD
    0x08001a80   0x08001a80   0x00000054   Code   RO         3361    i.ARC_ControlCavityHeating  arc.o
    0x08001ad4   0x08001ad4   0x0000000a   Code   RO         3362    i.ARC_GetCavityTemp  arc.o
    0x08001ade   0x08001ade   0x0000000a   Code   RO         3363    i.ARC_GetSampleTemp  arc.o
    0x08001ae8   0x08001ae8   0x000000dc   Code   RO         3364    i.ARC_Init          arc.o
    0x08001bc4   0x08001bc4   0x0000010c   Code   RO         3365    i.ARC_Process       arc.o
    0x08001cd0   0x08001cd0   0x00000154   Code   RO         3366    i.ARC_ReportData    arc.o
    0x08001e24   0x08001e24   0x00000090   Code   RO         3369    i.ARC_Start         arc.o
    0x08001eb4   0x08001eb4   0x00000034   Code   RO         3370    i.ARC_Stop          arc.o
    0x08001ee8   0x08001ee8   0x000000d4   Code   RO         3371    i.ARC_UpdateState   arc.o
    0x08001fbc   0x08001fbc   0x00000004   Code   RO          333    i.BusFault_Handler  stm32h7xx_it.o
    0x08001fc0   0x08001fc0   0x0000001c   Code   RO          529    i.Cache_Enable      sys.o
    0x08001fdc   0x08001fdc   0x00000034   Code   RO         1119    i.DMA_CalcBaseAndBitshift  stm32h7xx_hal_dma.o
    0x08002010   0x08002010   0x00000074   Code   RO         1120    i.DMA_CalcDMAMUXChannelBaseAndMask  stm32h7xx_hal_dma.o
    0x08002084   0x08002084   0x00000054   Code   RO         1121    i.DMA_CalcDMAMUXRequestGenBaseAndMask  stm32h7xx_hal_dma.o
    0x080020d8   0x080020d8   0x000000ae   Code   RO         1122    i.DMA_CheckFifoParam  stm32h7xx_hal_dma.o
    0x08002186   0x08002186   0x00000002   PAD
    0x08002188   0x08002188   0x000000b4   Code   RO         1123    i.DMA_SetConfig     stm32h7xx_hal_dma.o
    0x0800223c   0x0800223c   0x00000002   Code   RO          334    i.DebugMon_Handler  stm32h7xx_it.o
    0x0800223e   0x0800223e   0x00000002   PAD
    0x08002240   0x08002240   0x0000003c   Code   RO         3290    i.Get_ColdCompensation  adc.o
    0x0800227c   0x0800227c   0x00000060   Code   RO         3292    i.Get_Temperature   adc.o
    0x080022dc   0x080022dc   0x000006d8   Code   RO         2852    i.HAL_ADC_ConfigChannel  stm32h7xx_hal_adc.o
    0x080029b4   0x080029b4   0x00000002   Code   RO         2853    i.HAL_ADC_ConvCpltCallback  stm32h7xx_hal_adc.o
    0x080029b6   0x080029b6   0x00000002   Code   RO         2854    i.HAL_ADC_ConvHalfCpltCallback  stm32h7xx_hal_adc.o
    0x080029b8   0x080029b8   0x00000002   Code   RO         2856    i.HAL_ADC_ErrorCallback  stm32h7xx_hal_adc.o
    0x080029ba   0x080029ba   0x00000002   PAD
    0x080029bc   0x080029bc   0x00000358   Code   RO         2861    i.HAL_ADC_Init      stm32h7xx_hal_adc.o
    0x08002d14   0x08002d14   0x000000ec   Code   RO         3293    i.HAL_ADC_MspInit   adc.o
    0x08002e00   0x08002e00   0x00000154   Code   RO         2868    i.HAL_ADC_Start_DMA  stm32h7xx_hal_adc.o
    0x08002f54   0x08002f54   0x000000f8   Code   RO         1125    i.HAL_DMA_Abort_IT  stm32h7xx_hal_dma.o
    0x0800304c   0x0800304c   0x00000210   Code   RO         1130    i.HAL_DMA_Init      stm32h7xx_hal_dma.o
    0x0800325c   0x0800325c   0x00000150   Code   RO         1134    i.HAL_DMA_Start_IT  stm32h7xx_hal_dma.o
    0x080033ac   0x080033ac   0x0000024c   Code   RO         1312    i.HAL_GPIO_Init     stm32h7xx_hal_gpio.o
    0x080035f8   0x080035f8   0x00000010   Code   RO         1314    i.HAL_GPIO_ReadPin  stm32h7xx_hal_gpio.o
    0x08003608   0x08003608   0x00000008   Code   RO         1315    i.HAL_GPIO_TogglePin  stm32h7xx_hal_gpio.o
    0x08003610   0x08003610   0x0000000a   Code   RO         1316    i.HAL_GPIO_WritePin  stm32h7xx_hal_gpio.o
    0x0800361a   0x0800361a   0x00000002   PAD
    0x0800361c   0x0800361c   0x0000000c   Code   RO          679    i.HAL_GetTick       stm32h7xx_hal.o
    0x08003628   0x08003628   0x00000018   Code   RO          682    i.HAL_IncTick       stm32h7xx_hal.o
    0x08003640   0x08003640   0x0000001c   Code   RO          683    i.HAL_Init          stm32h7xx_hal.o
    0x0800365c   0x0800365c   0x0000004c   Code   RO          684    i.HAL_InitTick      stm32h7xx_hal.o
    0x080036a8   0x080036a8   0x00000002   Code   RO          451    i.HAL_MspInit       stm32h7xx_hal_msp.o
    0x080036aa   0x080036aa   0x00000020   Code   RO          977    i.HAL_NVIC_EnableIRQ  stm32h7xx_hal_cortex.o
    0x080036ca   0x080036ca   0x0000007a   Code   RO          983    i.HAL_NVIC_SetPriority  stm32h7xx_hal_cortex.o
    0x08003744   0x08003744   0x00000028   Code   RO          984    i.HAL_NVIC_SetPriorityGrouping  stm32h7xx_hal_cortex.o
    0x0800376c   0x0800376c   0x00000038   Code   RO         2007    i.HAL_RCCEx_GetD1SysClockFreq  stm32h7xx_hal_rcc_ex.o
    0x080037a4   0x080037a4   0x0000002c   Code   RO         2008    i.HAL_RCCEx_GetD3PCLK1Freq  stm32h7xx_hal_rcc_ex.o
    0x080037d0   0x080037d0   0x0000022c   Code   RO         2010    i.HAL_RCCEx_GetPLL2ClockFreq  stm32h7xx_hal_rcc_ex.o
    0x080039fc   0x080039fc   0x0000022c   Code   RO         2011    i.HAL_RCCEx_GetPLL3ClockFreq  stm32h7xx_hal_rcc_ex.o
    0x08003c28   0x08003c28   0x00000250   Code   RO         1898    i.HAL_RCC_ClockConfig  stm32h7xx_hal_rcc.o
    0x08003e78   0x08003e78   0x00000038   Code   RO         1902    i.HAL_RCC_GetHCLKFreq  stm32h7xx_hal_rcc.o
    0x08003eb0   0x08003eb0   0x0000002c   Code   RO         1904    i.HAL_RCC_GetPCLK1Freq  stm32h7xx_hal_rcc.o
    0x08003edc   0x08003edc   0x0000002c   Code   RO         1905    i.HAL_RCC_GetPCLK2Freq  stm32h7xx_hal_rcc.o
    0x08003f08   0x08003f08   0x00000244   Code   RO         1906    i.HAL_RCC_GetSysClockFreq  stm32h7xx_hal_rcc.o
    0x0800414c   0x0800414c   0x000006c4   Code   RO         1909    i.HAL_RCC_OscConfig  stm32h7xx_hal_rcc.o
    0x08004810   0x08004810   0x00000028   Code   RO          986    i.HAL_SYSTICK_CLKSourceConfig  stm32h7xx_hal_cortex.o
    0x08004838   0x08004838   0x00000034   Code   RO          988    i.HAL_SYSTICK_Config  stm32h7xx_hal_cortex.o
    0x0800486c   0x0800486c   0x00000002   Code   RO         2521    i.HAL_UARTEx_WakeupCallback  stm32h7xx_hal_uart_ex.o
    0x0800486e   0x0800486e   0x00000002   Code   RO         2185    i.HAL_UART_ErrorCallback  stm32h7xx_hal_uart.o
    0x08004870   0x08004870   0x00000014   Code   RO         2187    i.HAL_UART_GetState  stm32h7xx_hal_uart.o
    0x08004884   0x08004884   0x000001bc   Code   RO         2188    i.HAL_UART_IRQHandler  stm32h7xx_hal_uart.o
    0x08004a40   0x08004a40   0x00000078   Code   RO         2189    i.HAL_UART_Init     stm32h7xx_hal_uart.o
    0x08004ab8   0x08004ab8   0x00000098   Code   RO          590    i.HAL_UART_MspInit  usart.o
    0x08004b50   0x08004b50   0x000000e0   Code   RO         2194    i.HAL_UART_Receive_IT  stm32h7xx_hal_uart.o
    0x08004c30   0x08004c30   0x00000088   Code   RO          591    i.HAL_UART_RxCpltCallback  usart.o
    0x08004cb8   0x08004cb8   0x000000c6   Code   RO         2197    i.HAL_UART_Transmit  stm32h7xx_hal_uart.o
    0x08004d7e   0x08004d7e   0x00000002   Code   RO         2200    i.HAL_UART_TxCpltCallback  stm32h7xx_hal_uart.o
    0x08004d80   0x08004d80   0x00000004   Code   RO          335    i.HardFault_Handler  stm32h7xx_it.o
    0x08004d84   0x08004d84   0x00000044   Code   RO         3253    i.KEY_Init          key.o
    0x08004dc8   0x08004dc8   0x000000c0   Code   RO         3254    i.KEY_Scan          key.o
    0x08004e88   0x08004e88   0x00000058   Code   RO         3227    i.LED_Init          led.o
    0x08004ee0   0x08004ee0   0x00000004   Code   RO          336    i.MemManage_Handler  stm32h7xx_it.o
    0x08004ee4   0x08004ee4   0x00000002   Code   RO          337    i.NMI_Handler       stm32h7xx_it.o
    0x08004ee6   0x08004ee6   0x00000002   Code   RO          338    i.PendSV_Handler    stm32h7xx_it.o
    0x08004ee8   0x08004ee8   0x0000004c   Code   RO         3466    i.RELAY_Init        relay.o
    0x08004f34   0x08004f34   0x000000a8   Code   RO         3467    i.RELAY_Set         relay.o
    0x08004fdc   0x08004fdc   0x0000001a   Code   RO         3468    i.RELAY_SetAll      relay.o
    0x08004ff6   0x08004ff6   0x00000002   PAD
    0x08004ff8   0x08004ff8   0x0000009c   Code   RO          532    i.SCB_EnableDCache  sys.o
    0x08005094   0x08005094   0x0000007c   Code   RO          533    i.SCB_EnableICache  sys.o
    0x08005110   0x08005110   0x00000002   Code   RO          339    i.SVC_Handler       stm32h7xx_it.o
    0x08005112   0x08005112   0x00000008   Code   RO          340    i.SysTick_Handler   stm32h7xx_it.o
    0x0800511a   0x0800511a   0x00000002   PAD
    0x0800511c   0x0800511c   0x000000c4   Code   RO          534    i.SystemClock_Config  sys.o
    0x080051e0   0x080051e0   0x00000094   Code   RO          414    i.SystemInit        system_stm32h7xx.o
    0x08005274   0x08005274   0x000000ea   Code   RO         2202    i.UART_AdvFeatureConfig  stm32h7xx_hal_uart.o
    0x0800535e   0x0800535e   0x00000074   Code   RO         2203    i.UART_CheckIdleState  stm32h7xx_hal_uart.o
    0x080053d2   0x080053d2   0x00000018   Code   RO         2204    i.UART_DMAAbortOnError  stm32h7xx_hal_uart.o
    0x080053ea   0x080053ea   0x00000020   Code   RO         2214    i.UART_EndRxTransfer  stm32h7xx_hal_uart.o
    0x0800540a   0x0800540a   0x00000020   Code   RO         2215    i.UART_EndTransmit_IT  stm32h7xx_hal_uart.o
    0x0800542a   0x0800542a   0x0000008a   Code   RO         2217    i.UART_Receive_IT   stm32h7xx_hal_uart.o
    0x080054b4   0x080054b4   0x000009a4   Code   RO         2218    i.UART_SetConfig    stm32h7xx_hal_uart.o
    0x08005e58   0x08005e58   0x00000082   Code   RO         2219    i.UART_Transmit_IT  stm32h7xx_hal_uart.o
    0x08005eda   0x08005eda   0x0000006c   Code   RO         2220    i.UART_WaitOnFlagUntilTimeout  stm32h7xx_hal_uart.o
    0x08005f46   0x08005f46   0x00000002   PAD
    0x08005f48   0x08005f48   0x00000050   Code   RO          592    i.USART1_IRQHandler  usart.o
    0x08005f98   0x08005f98   0x00000004   Code   RO          341    i.UsageFault_Handler  stm32h7xx_it.o
    0x08005f9c   0x08005f9c   0x00000030   Code   RO         3666    i.__ARM_fpclassify  m_wv.l(fpclassify.o)
    0x08005fcc   0x08005fcc   0x00000010   Code   RO          990    i.__NVIC_GetPriorityGrouping  stm32h7xx_hal_cortex.o
    0x08005fdc   0x08005fdc   0x00000028   Code   RO          991    i.__NVIC_SetPriority  stm32h7xx_hal_cortex.o
    0x08006004   0x08006004   0x0000000e   Code   RO         3558    i._is_digit         c_w.l(__printf_wp.o)
    0x08006012   0x08006012   0x00000004   Code   RO          593    i._sys_exit         usart.o
    0x08006016   0x08006016   0x00000002   PAD
    0x08006018   0x08006018   0x00000014   Code   RO          489    i.delay_init        delay.o
    0x0800602c   0x0800602c   0x00000018   Code   RO          490    i.delay_ms          delay.o
    0x08006044   0x08006044   0x00000048   Code   RO          491    i.delay_us          delay.o
    0x0800608c   0x0800608c   0x0000001c   Code   RO          594    i.fputc             usart.o
    0x080060a8   0x080060a8   0x000001a4   Code   RO            4    i.main              main.o
    0x0800624c   0x0800624c   0x00000038   Code   RO          595    i.uart_init         usart.o
    0x08006284   0x08006284   0x0000002c   Code   RO         3661    locale$$code        c_w.l(lc_numeric_c.o)
    0x080062b0   0x080062b0   0x0000000a   Code   RO         3740    x$fpl$fpinit        fz_wv.l(fpinit.o)
    0x080062ba   0x080062ba   0x00000004   Code   RO         3583    x$fpl$printf1       fz_wv.l(printf1.o)
    0x080062be   0x080062be   0x00000000   Code   RO         3665    x$fpl$usenofp       fz_wv.l(usenofp.o)
    0x080062be   0x080062be   0x00000040   Data   RO          415    .constdata          system_stm32h7xx.o
    0x080062fe   0x080062fe   0x00000038   Data   RO         1136    .constdata          stm32h7xx_hal_dma.o
    0x08006336   0x08006336   0x00000030   Data   RO         2221    .constdata          stm32h7xx_hal_uart.o
    0x08006366   0x08006366   0x00000028   Data   RO         3531    .constdata          c_w.l(_printf_hex_int.o)
    0x0800638e   0x0800638e   0x00000011   Data   RO         3561    .constdata          c_w.l(__printf_flags_wp.o)
    0x0800639f   0x0800639f   0x00000001   PAD
    0x080063a0   0x080063a0   0x00000094   Data   RO         3632    .constdata          c_w.l(bigflt0.o)
    0x08006434   0x08006434   0x00000020   Data   RO         3807    Region$$Table       anon$$obj.o
    0x08006454   0x08006454   0x0000001c   Data   RO         3660    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08006470, Size: 0x00000000, Max: 0x00020000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM2 (Exec base: 0x24000000, Load base: 0x08006470, Size: 0x00000900, Max: 0x00050000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x24000000   0x08006470   0x00000008   Data   RW          416    .data               system_stm32h7xx.o
    0x24000008   0x08006478   0x00000004   Data   RW          493    .data               delay.o
    0x2400000c   0x0800647c   0x00000007   Data   RW          598    .data               usart.o
    0x24000013   0x08006483   0x00000001   PAD
    0x24000014   0x08006484   0x00000009   Data   RW          706    .data               stm32h7xx_hal.o
    0x2400001d   0x0800648d   0x00000001   Data   RW         3256    .data               key.o
    0x2400001e   0x0800648e   0x00000002   PAD
    0x24000020   0x08006490   0x00000034   Data   RW         3373    .data               arc.o
    0x24000054   0x080064c4   0x00000008   Data   RW         3470    .data               relay.o
    0x2400005c        -       0x00000148   Zero   RW          596    .bss                usart.o
    0x240001a4        -       0x000000fc   Zero   RW         3294    .bss                adc.o
    0x240002a0        -       0x00000060   Zero   RW         3669    .bss                c_w.l(libspace.o)
    0x24000300        -       0x00000200   Zero   RW          480    HEAP                startup_stm32h743xx.o
    0x24000500        -       0x00000400   Zero   RW          479    STACK               startup_stm32h743xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       680        100          0          0        252       4473   adc.o
      1402        468          0         52          0       8432   arc.o
       116          8          0          4          0       2175   delay.o
       260         16          0          1          0       1734   key.o
        88         10          0          0          0        943   led.o
       420        150          0          0          0    1210263   main.o
       270         16          0          8          0       2628   relay.o
        64         26        664          0       1536        868   startup_stm32h743xx.o
       140         26          0          9          0       3658   stm32h7xx_hal.o
      3296        106          0          0          0       9857   stm32h7xx_hal_adc.o
       342         22          0          0          0      39725   stm32h7xx_hal_cortex.o
      1718         86         56          0          0       8493   stm32h7xx_hal_dma.o
       622         54          0          0          0       3988   stm32h7xx_hal_gpio.o
         2          0          0          0          0        490   stm32h7xx_hal_msp.o
      3048         86          0          0          0       6272   stm32h7xx_hal_rcc.o
      1212         70          0          0          0       3743   stm32h7xx_hal_rcc_ex.o
      4292        140         48          0          0      14608   stm32h7xx_hal_uart.o
         2          0          0          0          0       1052   stm32h7xx_hal_uart_ex.o
        32          0          0          0          0       4434   stm32h7xx_it.o
       504         26          0          0          0      37315   sys.o
       148         18         64          8          0       1362   system_stm32h7xx.o
       456         62          0          7        328       5805   usart.o

    ----------------------------------------------------------------------
     19134       <USER>        <GROUP>         92       2116    1372318   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        20          0          0          3          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       312          4         17          0          0         92   __printf_flags_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
        88          4         40          0          0         88   _printf_hex_int.o
       178          0          0          0          0         88   _printf_intcommon.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
         6          0          0          0          0          0   _printf_x.o
        10          0          0          0          0         68   _sputc.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        22          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
       238          0          0          0          0        100   lludivv7m.o
        24          4          0          0          0         84   noretval__2printf.o
        40          6          0          0          0         84   noretval__2sprintf.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       104          0          0          0          0         68   strcmpv7m_pel.o
        62          0          0          0          0         76   strlen.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        10          0          0          0          0        116   fpinit.o
         4          0          0          0          0        116   printf1.o
         0          0          0          0          0          0   usenofp.o
        48          0          0          0          0        124   fpclassify.o

    ----------------------------------------------------------------------
      5480        <USER>        <GROUP>          0         96       3624   Library Totals
        12          0          1          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      5406        218        233          0         96       3268   c_w.l
        14          0          0          0          0        232   fz_wv.l
        48          0          0          0          0        124   m_wv.l

    ----------------------------------------------------------------------
      5480        <USER>        <GROUP>          0         96       3624   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     24614       1708       1098         92       2212    1364274   Grand Totals
     24614       1708       1098         92       2212    1364274   ELF Image Totals
     24614       1708       1098         92          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                25712 (  25.11kB)
    Total RW  Size (RW Data + ZI Data)              2304 (   2.25kB)
    Total ROM Size (Code + RO Data + RW Data)      25804 (  25.20kB)

==============================================================================

