#include "adc.h"
#include "delay.h"
#include "stdio.h"  // ����stdio.hͷ�ļ�

// ADC���
ADC_HandleTypeDef ADC1_Handler;
DMA_HandleTypeDef DMA1_Handler;

// ADCת�����������
u16 ADC_ConvertedValue[ADC_CHANNEL_NUM];

/**
 * @brief ADC��ʼ��
 * @param None
 * @retval None
 */
void ADC_Init(void)
{
    // ����ADCͨ��
    ADC_ChannelConfTypeDef ADC_ChanConf;
    u8 i;
    
    // ����ADC
    ADC1_Handler.Instance = ADC1;
    ADC1_Handler.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV4;
    ADC1_Handler.Init.Resolution = ADC_RESOLUTION_16B;
    ADC1_Handler.Init.ScanConvMode = ADC_SCAN_ENABLE;
    ADC1_Handler.Init.EOCSelection = ADC_EOC_SINGLE_CONV;
    ADC1_Handler.Init.LowPowerAutoWait = DISABLE;
    ADC1_Handler.Init.ContinuousConvMode = ENABLE;
    ADC1_Handler.Init.NbrOfConversion = ADC_CHANNEL_NUM;
    ADC1_Handler.Init.DiscontinuousConvMode = DISABLE;
    ADC1_Handler.Init.NbrOfDiscConversion = 0;
    ADC1_Handler.Init.ExternalTrigConv = ADC_SOFTWARE_START;
    ADC1_Handler.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_NONE;
    ADC1_Handler.Init.ConversionDataManagement = ADC_CONVERSIONDATA_DMA_CIRCULAR;
    ADC1_Handler.Init.Overrun = ADC_OVR_DATA_PRESERVED;
    ADC1_Handler.Init.LeftBitShift = ADC_LEFTBITSHIFT_NONE;
    ADC1_Handler.Init.OversamplingMode = DISABLE;
    HAL_ADC_Init(&ADC1_Handler);
    
    // ����ѹ��������ͨ��
    for(i=0; i<PRESSURE_CHANNEL_NUM; i++)
    {
        ADC_ChanConf.Channel = ADC_CHANNEL_0 + i;
        ADC_ChanConf.Rank = i + 1;
        ADC_ChanConf.SamplingTime = ADC_SAMPLETIME_810CYCLES_5;
        ADC_ChanConf.SingleDiff = ADC_SINGLE_ENDED;
        ADC_ChanConf.OffsetNumber = ADC_OFFSET_NONE;
        ADC_ChanConf.Offset = 0;
        HAL_ADC_ConfigChannel(&ADC1_Handler, &ADC_ChanConf);
    }
    
    // �����ȵ�żͨ��
    for(i=0; i<THERMO_CHANNEL_NUM; i++)
    {
        ADC_ChanConf.Channel = ADC_CHANNEL_8 + i;
        ADC_ChanConf.Rank = PRESSURE_CHANNEL_NUM + i + 1;
        ADC_ChanConf.SamplingTime = ADC_SAMPLETIME_810CYCLES_5;
        ADC_ChanConf.SingleDiff = ADC_SINGLE_ENDED;
        ADC_ChanConf.OffsetNumber = ADC_OFFSET_NONE;
        ADC_ChanConf.Offset = 0;
        HAL_ADC_ConfigChannel(&ADC1_Handler, &ADC_ChanConf);
    }
    
    // ������˲���ͨ��
    ADC_ChanConf.Channel = ADC_CHANNEL_12;
    ADC_ChanConf.Rank = PRESSURE_CHANNEL_NUM + THERMO_CHANNEL_NUM + 1;
    ADC_ChanConf.SamplingTime = ADC_SAMPLETIME_810CYCLES_5;
    ADC_ChanConf.SingleDiff = ADC_SINGLE_ENDED;
    ADC_ChanConf.OffsetNumber = ADC_OFFSET_NONE;
    ADC_ChanConf.Offset = 0;
    HAL_ADC_ConfigChannel(&ADC1_Handler, &ADC_ChanConf);
    
    // ��������ģ������ͨ��
    for(i=0; i<OTHER_CHANNEL_NUM; i++)
    {
        ADC_ChanConf.Channel = ADC_CHANNEL_13 + i;
        ADC_ChanConf.Rank = PRESSURE_CHANNEL_NUM + THERMO_CHANNEL_NUM + 1 + i + 1;
        ADC_ChanConf.SamplingTime = ADC_SAMPLETIME_810CYCLES_5;
        ADC_ChanConf.SingleDiff = ADC_SINGLE_ENDED;
        ADC_ChanConf.OffsetNumber = ADC_OFFSET_NONE;
        ADC_ChanConf.Offset = 0;
        HAL_ADC_ConfigChannel(&ADC1_Handler, &ADC_ChanConf);
    }
    
    // ����ADCת��
    ADC_Start();
    
    printf("ADC��ʼ�����\r\n");
}

/**
 * @brief ADC�ײ��ʼ��
 * @param hadc: ADC���
 * @retval None
 */
void HAL_ADC_MspInit(ADC_HandleTypeDef* hadc)
{
    GPIO_InitTypeDef GPIO_Initure;
    
    if(hadc->Instance == ADC1)
    {
        // ʹ��ʱ��
			/*
        __HAL_RCC_ADC1_CLK_ENABLE();
        __HAL_RCC_GPIOA_CLK_ENABLE();
        __HAL_RCC_GPIOB_CLK_ENABLE();
        __HAL_RCC_GPIOC_CLK_ENABLE();
        __HAL_RCC_DMA1_CLK_ENABLE();
			*/
			
			  /* ����ʱ��ʹ�� */
			ADCx_RCC_CLK_ENABLE();
  
			/* ADת��ͨ������ʱ��ʹ�� */
			ADC_GPIO_ClK_ENABLE();
 
			/* DMA ����ʱ��ʹ�� */
			DMAx_CHANNELx_CLK_ENABLE();
        
        // ����GPIO
        
        // ����ѹ��������GPIO
        GPIO_Initure.Pin = GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_3|
                           GPIO_PIN_4|GPIO_PIN_5|GPIO_PIN_6|GPIO_PIN_7;
        GPIO_Initure.Mode = GPIO_MODE_ANALOG;
        GPIO_Initure.Pull = GPIO_NOPULL;
        HAL_GPIO_Init(GPIOA, &GPIO_Initure);
        
        // �����ȵ�żGPIO
        GPIO_Initure.Pin = GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_3;
        HAL_GPIO_Init(GPIOB, &GPIO_Initure);
        
        // ������˲���������ģ������GPIO
        GPIO_Initure.Pin = GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_3;
        HAL_GPIO_Init(GPIOC, &GPIO_Initure);
        
        // ����DMA
        DMA1_Handler.Instance = DMA1_Stream0;
        DMA1_Handler.Init.Request = DMA_REQUEST_ADC1;
        DMA1_Handler.Init.Direction = DMA_PERIPH_TO_MEMORY;
        DMA1_Handler.Init.PeriphInc = DMA_PINC_DISABLE;
        DMA1_Handler.Init.MemInc = DMA_MINC_ENABLE;
        DMA1_Handler.Init.PeriphDataAlignment = DMA_PDATAALIGN_HALFWORD;
        DMA1_Handler.Init.MemDataAlignment = DMA_MDATAALIGN_HALFWORD;
        DMA1_Handler.Init.Mode = DMA_CIRCULAR;
        DMA1_Handler.Init.Priority = DMA_PRIORITY_MEDIUM;
        DMA1_Handler.Init.FIFOMode = DMA_FIFOMODE_DISABLE;
        HAL_DMA_Init(&DMA1_Handler);
        
        // ����DMA��ADC
        __HAL_LINKDMA(hadc, DMA_Handle, DMA1_Handler);
    }
}

/**
 * @brief ����ADCת��
 * @param None
 * @retval None
 */
void ADC_Start(void)
{
    HAL_ADC_Start_DMA(&ADC1_Handler, (uint32_t*)ADC_ConvertedValue, ADC_CHANNEL_NUM);
}

/**
 * @brief ֹͣADCת��
 * @param None
 * @retval None
 */
void ADC_Stop(void)
{
    HAL_ADC_Stop_DMA(&ADC1_Handler);
}

/**
 * @brief ��ȡѹ��ֵ
 * @param channel: ͨ����(0-7)
 * @retval ѹ��ֵ(MPa)
 */
float Get_Pressure(u8 channel)
{
    u16 adcValue;
    float current;
    float pressure;
    
    if(channel >= PRESSURE_CHANNEL_NUM) return 0;
    
    adcValue = ADC_ConvertedValue[PRESSURE_CHANNEL_START + channel];
    
    // ��ADCֵת��Ϊ����ֵ(mA)������������Ϊ65535
    current = (float)adcValue / 65535.0f * 20.0f;
    
    // ������ֵת��Ϊѹ��ֵ(MPa)������0-20mA��Ӧ0-1MPa
    pressure = current / 20.0f * 1.0f;
    
    return pressure;
}

/**
 * @brief ��ȡ�¶�ֵ
 * @param channel: ͨ����(0-3)
 * @retval �¶�ֵ(��C)
 */
float Get_Temperature(u8 channel)
{
    u16 adcValue;
    float voltage;
    float coldTemp;
    float temperature;
    
    if(channel >= THERMO_CHANNEL_NUM) return 0;
    
    adcValue = ADC_ConvertedValue[THERMO_CHANNEL_START + channel];
    
    // ��ADCֵת��Ϊ��ѹֵ(mV)������������Ϊ65535����Ӧ3.3V
    voltage = (float)adcValue / 65535.0f * 3300.0f;
    
    // ��ȡ��˲����¶�
    coldTemp = Get_ColdCompensation();
    
    // ����ѹֵת��Ϊ�¶�ֵ(��C)������N���ȵ�ż��Լ39��V/��C
    temperature = voltage / 39.0f + coldTemp;
    
    return temperature;
}

/**
 * @brief ��ȡ��˲���ֵ
 * @param None
 * @retval ��˲����¶�(��C)
 */
float Get_ColdCompensation(void)
{
    u16 adcValue;
    float voltage;
    float temperature;
    
    adcValue = ADC_ConvertedValue[COLD_COMP_CHANNEL];
    
    // ��ADCֵת��Ϊ��ѹֵ(mV)������������Ϊ65535����Ӧ3.3V
    voltage = (float)adcValue / 65535.0f * 3300.0f;
    
    // ����ʹ��LM35�¶ȴ�������10mV/��C
    temperature = voltage / 10.0f;
    
    return temperature;
}

/**
 * @brief ��ȡ����ģ������ֵ
 * @param channel: ͨ����(0-2)
 * @retval ��ѹֵ(V)
 */
float Get_AnalogInput(u8 channel)
{
    u16 adcValue;
    float voltage;
    
    if(channel >= OTHER_CHANNEL_NUM) return 0;
    
    adcValue = ADC_ConvertedValue[OTHER_CHANNEL_START + channel];
    
    // ��ADCֵת��Ϊ��ѹֵ(V)������������Ϊ65535����Ӧ3.3V
    voltage = (float)adcValue / 65535.0f * 3.3f;
    
    return voltage;
} 