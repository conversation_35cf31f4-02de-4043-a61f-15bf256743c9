#ifndef _RELAY_H
#define _RELAY_H
#include "sys.h"

// 继电器通道数量
#define RELAY_CHANNEL_NUM  8

// 继电器引脚定义
#define RELAY1(n)      (n?HAL_GPIO_WritePin(GPIOE,GPIO_PIN_9,GPIO_PIN_SET):HAL_GPIO_WritePin(GPIOE,GPIO_PIN_9,GPIO_PIN_RESET))
#define RELAY2(n)      (n?HAL_GPIO_WritePin(GPIOE,GPIO_PIN_11,GPIO_PIN_SET):HAL_GPIO_WritePin(GPIOE,GPIO_PIN_11,GPIO_PIN_RESET))
#define RELAY3(n)      (n?HAL_GPIO_WritePin(GPIOE,GPIO_PIN_13,GPIO_PIN_SET):HAL_GPIO_WritePin(GPIOE,GPIO_PIN_13,GPIO_PIN_RESET))
#define RELAY4(n)      (n?HAL_GPIO_WritePin(GPIOE,GPIO_PIN_14,GPIO_PIN_SET):H<PERSON>_GPIO_WritePin(GPIOE,GPIO_PIN_14,GP<PERSON>_PIN_RESET))
//#define RELAY5(n)      (n?HAL_GPIO_WritePin(GPIOE,GPIO_PIN_4,GPIO_PIN_SET):HAL_GPIO_WritePin(GPIOE,GPIO_PIN_4,GPIO_PIN_RESET))
//#define RELAY6(n)      (n?HAL_GPIO_WritePin(GPIOE,GPIO_PIN_5,GPIO_PIN_SET):HAL_GPIO_WritePin(GPIOE,GPIO_PIN_5,GPIO_PIN_RESET))
//#define RELAY7(n)      (n?HAL_GPIO_WritePin(GPIOE,GPIO_PIN_6,GPIO_PIN_SET):HAL_GPIO_WritePin(GPIOE,GPIO_PIN_6,GPIO_PIN_RESET))
//#define RELAY8(n)      (n?HAL_GPIO_WritePin(GPIOE,GPIO_PIN_7,GPIO_PIN_SET):HAL_GPIO_WritePin(GPIOE,GPIO_PIN_7,GPIO_PIN_RESET))

// 函数声明
void RELAY_Init(void);                  // 继电器初始化
void RELAY_Set(u8 channel, u8 state);   // 设置继电器状态
void RELAY_SetAll(u8 state);            // 设置所有继电器状态
u8 RELAY_Get(u8 channel);               // 获取继电器状态

#endif 