#include "relay.h"
#include "delay.h"

// 继电器状态缓冲区
static u8 RELAY_State[RELAY_CHANNEL_NUM];

/**
 * @brief 继电器初始化
 * @param None
 * @retval None
 */
void RELAY_Init(void)
{
    GPIO_InitTypeDef GPIO_Initure;
    
    __HAL_RCC_GPIOE_CLK_ENABLE(); // 使能GPIOE时钟
    
    // 配置继电器控制引脚
    //GPIO_Initure.Pin = GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_3|
     //                  GPIO_PIN_4|GPIO_PIN_5|GPIO_PIN_6|GPIO_PIN_7;
	  GPIO_Initure.Pin = GPIO_PIN_9|GPIO_PIN_11|GPIO_PIN_13|GPIO_PIN_14;
    GPIO_Initure.Mode = GPIO_MODE_OUTPUT_PP;  // 推挽输出
    GPIO_Initure.Pull = GPIO_PULLDOWN;        // 下拉
    GPIO_Initure.Speed = GPIO_SPEED_FREQ_LOW; // 低速
    HAL_GPIO_Init(GPIOE, &GPIO_Initure);      // 初始化GPIO
    
    // 初始状态，关闭所有继电器
    RELAY_SetAll(0);
}

/**
 * @brief 设置继电器状态
 * @param channel: 通道号(1-8)
 * @param state: 状态(0-关闭，1-打开)
 * @retval None
 */
void RELAY_Set(u8 channel, u8 state)
{
    if(channel < 1 || channel > RELAY_CHANNEL_NUM)
        return;
    
    // 更新状态缓冲区
    RELAY_State[channel-1] = state;
    
    // 设置继电器状态
    switch(channel)
    {
        case 1: RELAY1(state); break;
        case 2: RELAY2(state); break;
        case 3: RELAY3(state); break;
        case 4: RELAY4(state); break;
        default: break;
    }
}

/**
 * @brief 设置所有继电器状态
 * @param state: 状态(0-关闭，1-打开)
 * @retval None
 */
void RELAY_SetAll(u8 state)
{
    u8 i;
    
    // 设置所有继电器
    for(i=1; i<=RELAY_CHANNEL_NUM; i++)
    {
        RELAY_Set(i, state);
    }
}

/**
 * @brief 获取继电器状态
 * @param channel: 通道号(1-8)
 * @retval 继电器状态(0-关闭，1-打开)
 */
u8 RELAY_Get(u8 channel)
{
    if(channel < 1 || channel > RELAY_CHANNEL_NUM)
        return 0;
    
    return RELAY_State[channel-1];
} 