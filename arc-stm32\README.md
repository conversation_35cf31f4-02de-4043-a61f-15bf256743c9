# ARC绝热分析仪

## 项目概述

ARC（Accelerating Rate Calorimetry）绝热分析仪是一种用于测量物质热稳定性的仪器。该项目基于STM32H7系列微控制器实现，通过精确控制样品和腔体温度，监测样品温度变化速率，实现绝热分析功能。

本系统可用于化学反应热分析、材料热稳定性研究、安全性评估等领域，具有高精度、高可靠性的特点。

## 功能特点

- **温度精确控制**：样品均匀加热，同步腔体加热，保持腔体温度高于样品温度
- **自动控温逻辑**：当腔体与样品温度差值达到预设值时，自动停止腔体加热
- **智能加热循环**：腔体温度自然回落，当与样品温度接近时，自动重新加热
- **实验自动终止**：当样品温度变化速率达到0.02°C/min时，自动停止实验
- **实时数据监测**：通过串口输出温度数据和系统状态
- **LED状态指示**：通过LED指示灯显示系统运行状态
- **数据上报功能**：通过串口将实验数据上报给上位机，用于绘制曲线图和数据记录

## 硬件要求

### 主控制器
- STM32H723ZGT6微控制器

### 传感器
- N型铠装热电偶（用于温度测量）
- 冷端补偿传感器（如LM35）

### 执行器
- 4个继电器控制的加热棒（用于腔体加热）
- 1个继电器控制的加热器（用于样品加热）

### 接口
- 串口通信（用于数据输出和调试）
- LED指示灯（用于状态显示）
- 按键（用于控制实验开始和停止）

## 详细接线说明

### ADC接线（温度传感器）

| 传感器 | STM32引脚 | 功能 |
|-------|----------|------|
| 样品热电偶+ | PB0 | 样品温度测量 |
| 样品热电偶- | GND | 接地 |
| 腔体热电偶+ | PB1 | 腔体温度测量 |
| 腔体热电偶- | GND | 接地 |
| 冷端补偿传感器(LM35)信号 | PC0 | 冷端补偿温度测量 |
| 冷端补偿传感器(LM35)电源 | 3.3V | 传感器供电 |
| 冷端补偿传感器(LM35)接地 | GND | 接地 |

### 继电器接线

| 继电器通道 | STM32引脚 | 功能 |
|-----------|----------|------|
| 腔体加热器1 | PE9 | 控制腔体加热棒1 |
| 腔体加热器2 | PE11 | 控制腔体加热棒2 |
| 腔体加热器3 | PE13 | 控制腔体加热棒3 |
| 腔体加热器4 | PE14 | 控制腔体加热棒4 |
| 样品加热器 | PE4 | 控制样品加热器 |

#### 继电器模块接线说明
- VCC: 连接到STM32的5V或3.3V（根据继电器模块要求）
- GND: 连接到STM32的GND
- IN1-IN5: 分别连接到PE0-PE4
- 继电器常开端(NO): 连接到加热器电源正极
- 继电器公共端(COM): 连接到加热器
- 加热器另一端: 连接到电源负极

### 按键接线

| 按键 | STM32引脚 | 功能 |
|------|----------|------|
| KEY0 | PF1 | 开始实验 |
| KEY1 | PF2 | 停止实验 |

#### 按键接线说明
- 按键一端: 连接到对应的STM32引脚
- 按键另一端: 连接到GND
- 上拉电阻(10kΩ): 连接在STM32引脚和VCC之间

### LED指示灯接线

| LED | STM32引脚 | 功能 |
|-----|----------|------|
| LED0 | PB1 | 系统运行状态指示 |
| LED1 | PB0 | 实验状态指示 |

#### LED接线说明
- LED阳极: 通过限流电阻(220Ω)连接到VCC
- LED阴极: 连接到对应的STM32引脚

### 串口通信接线

| 功能 | STM32引脚 | 外部连接 |
|------|----------|---------|
| USART1_TX | PA9 | USB转TTL模块RX |
| USART1_RX | PA10 | USB转TTL模块TX |
| GND | GND | USB转TTL模块GND |

### 电源接线

| 设备 | 电源要求 |
|------|---------|
| STM32开发板 | 5V DC，至少500mA |
| 加热器电源 | 根据加热器规格，通常为220V AC或24V DC |
| 继电器模块 | 5V或3.3V逻辑电平，根据模块规格 |

### 接线注意事项

1. **热电偶接线**:
   - 热电偶信号微弱，建议使用屏蔽线减少干扰
   - 确保热电偶极性正确连接
   - 热电偶应通过专用放大器电路连接到ADC引脚

2. **继电器接线**:
   - 高压部分应由专业人员操作
   - 确保继电器额定电流大于加热器工作电流
   - 加热器电源应单独供电，不要与控制电路共用

3. **信号线布置**:
   - 信号线应远离电源线和高压线，减少干扰
   - 模拟信号线应尽量短，减少噪声
   - 必要时使用光耦隔离信号

4. **接地处理**:
   - 所有地线应连接到同一参考点
   - 模拟地和数字地应分开，最终在一点连接
   - 大功率设备应单独接地

## 软件架构

### 模块结构
- **ADC模块**：负责温度传感器数据采集
- **继电器模块**：负责控制加热器
- **ARC模块**：实现绝热分析核心算法
- **主程序**：系统初始化和主循环

### 文件说明
- **adc.c/h**：ADC初始化和数据采集
- **relay.c/h**：继电器控制
- **arc.c/h**：绝热分析算法
- **main.c**：主程序入口

### 关键参数
- 温度差值预设：5.0°C（可调）
- 温度变化速率阈值：0.02°C/min（可调）
- 温度采样间隔：1000ms
- 速率计算周期：60000ms
- 数据上报间隔：1000ms

## 数据上报功能

### 通信协议

ARC绝热分析仪使用以下串口通信协议向上位机发送实验数据：

```
$ARC,<时间戳>,<样品温度>,<腔体温度>,<温度差值>,<温度变化速率>,<系统状态>,<校验和>*
```

#### 字段说明：
- `$ARC`: 数据帧起始标识
- `<时间戳>`: 实验开始后的时间，单位为秒，保留1位小数
- `<样品温度>`: 样品当前温度，单位为°C，保留2位小数
- `<腔体温度>`: 腔体当前温度，单位为°C，保留2位小数
- `<温度差值>`: 腔体与样品的温度差，单位为°C，保留2位小数
- `<温度变化速率>`: 样品温度变化速率，单位为°C/min，保留4位小数
- `<系统状态>`: 系统当前状态，0=空闲，1=加热，2=冷却，3=完成
- `<校验和>`: 从$后到*前所有字符的异或校验值，十六进制表示

#### 示例：
```
$ARC,120.5,35.25,40.15,4.90,0.0152,1,3F*
```

### 上位机软件要求

上位机软件需要实现以下功能：

1. **串口通信**：
   - 配置串口参数（波特率115200，8数据位，1停止位，无校验）
   - 接收和解析数据帧
   - 校验数据完整性

2. **数据处理**：
   - 解析各个数据字段
   - 存储数据到内存和本地文件
   - 数据格式转换（如需要）

3. **图形显示**：
   - 实时绘制温度曲线图
   - 显示样品温度、腔体温度、温度差值和变化速率
   - 标记系统状态变化点

4. **数据记录**：
   - 将数据保存为CSV或Excel格式
   - 记录实验开始时间、结束时间和总时长
   - 保存实验参数设置

### 数据文件格式

CSV格式数据文件示例：

```csv
时间戳(s),样品温度(°C),腔体温度(°C),温度差值(°C),温度变化速率(°C/min),系统状态
0.0,25.00,25.00,0.00,0.0000,1
1.0,25.05,25.15,0.10,0.0500,1
2.0,25.10,25.35,0.25,0.0500,1
...
120.5,35.25,40.15,4.90,0.0152,1
...
```

### 上位机软件推荐

- **Python + PySerial + Matplotlib**：适合快速开发，跨平台
- **C# + .NET**：Windows平台下用户界面友好
- **LabVIEW**：适合实验室环境，开发速度快
- **MATLAB**：数据分析能力强，适合科研环境

## 使用说明

### 编译环境
- Keil MDK 5.0或更高版本
- ARM Compiler 5.06或更高版本

### 编译步骤
1. 打开Keil MDK
2. 加载项目文件(UART.uvprojx)
3. 编译项目
4. 下载到STM32H723ZGT6开发板

### 操作方法
1. 上电后，系统自动初始化
2. 按KEY0键开始实验
3. 按KEY1键停止实验
4. 通过串口监视器查看实验数据（波特率115200）
5. 使用上位机软件接收数据并绘制曲线图

### LED指示
- LED0闪烁：系统正在运行
- LED1常亮：加热状态
- LED1闪烁：冷却状态
- LED1熄灭：实验完成或空闲状态

## 实验流程

1. **初始阶段**：
   - 样品和腔体同步加热
   - 腔体温度上升速度快于样品温度

2. **控制阶段**：
   - 当腔体温度与样品温度差值达到预设值（默认5°C）时，腔体停止加热
   - 腔体温度自然回落
   - 当腔体温度与样品温度接近相等时，腔体重新开始加热
   - 循环上述过程

3. **结束阶段**：
   - 当样品温度变化速率低于预设阈值（默认0.02°C/min）时，实验自动结束
   - 所有加热器关闭
   - LED1熄灭表示实验完成

## 参数调整

可以通过修改以下函数调整系统参数：

```c
// 设置温度差值
ARC_SetTempDiff(float diff);

// 设置速率阈值
ARC_SetRateThreshold(float rate);
```

也可以直接修改arc.h文件中的宏定义：

```c
#define TEMP_DIFF_PRESET    5.0f        // 腔体与样品温度预设差值 (°C)
#define TEMP_RATE_THRESHOLD 0.02f       // 样品温度变化速率阈值 (°C/min)
#define DATA_REPORT_INTERVAL 1000       // 数据上报间隔(ms)
```

## 注意事项

1. **安全警告**：
   - 实验过程中可能涉及高温，请注意安全
   - 确保加热器和传感器正确连接，避免短路
   - 高压部分应由专业人员操作

2. **传感器校准**：
   - 首次使用前应对热电偶进行校准
   - 可能需要调整温度转换公式以匹配特定的传感器

3. **电源要求**：
   - 确保电源稳定，避免电压波动影响测量精度
   - 继电器控制的加热器可能需要单独的电源供应

4. **数据处理**：
   - 可以通过串口将数据保存到计算机进行进一步分析
   - 建议使用滤波算法处理温度数据，减少噪声影响

5. **数据上报**：
   - 确保串口通信参数设置正确
   - 验证数据校验和，确保数据完整性
   - 数据上报不应影响主控制逻辑

## 故障排除

| 问题 | 可能原因 | 解决方法 |
|------|---------|---------|
| 温度读数不准确 | 传感器校准问题 | 调整转换公式或重新校准传感器 |
| 加热器不工作 | 继电器连接问题 | 检查继电器接线和控制信号 |
| 系统无响应 | 程序死循环 | 检查代码中的循环条件和中断处理 |
| 编译错误 | 缺少库文件 | 确保包含所有必要的HAL库文件 |
| 热电偶读数波动大 | 信号干扰 | 使用屏蔽线并增加滤波算法 |
| 继电器不动作 | 驱动电流不足 | 检查GPIO输出能力，必要时增加驱动电路 |
| 上位机无法接收数据 | 串口配置错误 | 检查波特率和其他通信参数 |
| 数据校验错误 | 通信干扰 | 检查串口连接，必要时降低波特率 |

## 未来改进

1. 添加LCD显示界面，实时显示温度和系统状态
2. 实现PID控制算法，提高温度控制精度
3. 增加网络通信功能，实现远程监控
4. 添加SD卡存储功能，直接在设备上记录数据
5. 开发更完善的上位机软件，提供更多数据分析功能

## 联系方式

如有任何问题或建议，请联系项目维护者。

---

*注：本项目基于STM32H7系列微控制器开发，使用HAL库进行底层驱动。请确保在使用前了解相关硬件和软件知识。高温和高压设备操作存在安全风险，请严格遵守安全规程。* 