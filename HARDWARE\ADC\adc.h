#ifndef _ADC_H
#define _ADC_H
#include "sys.h"
#include "stm32h7xx_hal.h"
#include "relay.h"  // 添加继电器头文件




/****************************ADC_AIN1 AIN2 引脚配置**********************************/

#define ADCx_RCC_CLK_ENABLE()            __HAL_RCC_ADC12_CLK_ENABLE()
#define ADCx                             ADC1

#define ADC_GPIO_ClK_ENABLE()            __HAL_RCC_GPIOF_CLK_ENABLE()
#define ADC_GPIO                         GPIOF
#define ADC_GPIO_PIN                     GPIO_PIN_11        
#define ADC_CHANNEL                      ADC_CHANNEL_2   
             
#define DMAx_Streamx                     DMA1_Stream0
#define DMAx_CHANNELx_CLK_ENABLE()       __HAL_RCC_DMA1_CLK_ENABLE()
#define DMA_REQUEST_ADCx                 DMA_REQUEST_ADC1             
#define DMAx_Streamx_IRQn                DMA1_Stream0_IRQn                        
#define DMAx_Streamx_IRQndler            DMA1_Stream0_IRQHandler     
      
/****************************ADC_AIN2Òý½ÅÅäÖÃ**********************************/
//#define ADCx_RCC_CLK_ENABLE()            __HAL_RCC_ADC12_CLK_ENABLE()
//#define ADCx                             ADC1

//#define ADC_GPIO_ClK_ENABLE()            __HAL_RCC_GPIOF_CLK_ENABLE()
//#define ADC_GPIO                         GPIOF
//#define ADC_GPIO_PIN                     GPIO_PIN_12       
//#define ADC_CHANNEL                      ADC_CHANNEL_6
//                   
//#define DMAx_Streamx                     DMA1_Stream0
//#define DMAx_CHANNELx_CLK_ENABLE()       __HAL_RCC_DMA1_CLK_ENABLE()
//#define DMA_REQUEST_ADCx                 DMA_REQUEST_ADC1             
//#define DMAx_Streamx_IRQn                DMA1_Stream0_IRQn                        
//#define DMAx_Streamx_IRQndler            DMA1_Stream0_IRQHandler     
                      
/****************************ADC_AIN3Òý½ÅÅäÖÃ**********************************/
//#define ADCx_RCC_CLK_ENABLE()            __HAL_RCC_ADC3_CLK_ENABLE()
//#define ADCx                             ADC3

//#define ADC_GPIO_ClK_ENABLE()            __HAL_RCC_GPIOH_CLK_ENABLE()
//#define ADC_GPIO                         GPIOH
//#define ADC_GPIO_PIN                     GPIO_PIN_5       
//#define ADC_CHANNEL                      ADC_CHANNEL_16  

//#define DMAx_Streamx                     DMA1_Stream1
//#define DMAx_CHANNELx_CLK_ENABLE()       __HAL_RCC_DMA1_CLK_ENABLE()
//#define DMA_REQUEST_ADCx                 DMA_REQUEST_ADC3             
//#define DMAx_Streamx_IRQn                DMA1_Stream1_IRQn                        
//#define DMAx_Streamx_IRQndler            DMA1_Stream1_IRQHandler     
                 
/****************************ADC_AIN4Òý½ÅÅäÖÃ**********************************/
//#define ADCx_RCC_CLK_ENABLE()            __HAL_RCC_ADC3_CLK_ENABLE()
//#define ADCx                             ADC3

//#define ADC_GPIO_ClK_ENABLE()            __HAL_RCC_GPIOH_CLK_ENABLE()
//#define ADC_GPIO                         GPIOH
//#define ADC_GPIO_PIN                     GPIO_PIN_4        
//#define ADC_CHANNEL                      ADC_CHANNEL_15     

//#define DMAx_Streamx                     DMA1_Stream1
//#define DMAx_CHANNELx_CLK_ENABLE()       __HAL_RCC_DMA1_CLK_ENABLE()
//#define DMA_REQUEST_ADCx                 DMA_REQUEST_ADC3             
//#define DMAx_Streamx_IRQn                DMA1_Stream1_IRQn                        
//#define DMAx_Streamx_IRQndler            DMA1_Stream1_IRQHandler     

// ADC通道定义
#define ADC_CHANNEL_NUM     16  // ADC通道数量

// 压力传感器通道定义 (0-20mA)
#define PRESSURE_CHANNEL_NUM 8  // 压力传感器通道数量
#define PRESSURE_CHANNEL_START 0 // 压力传感器起始通道

// 热电偶通道定义
#define THERMO_CHANNEL_NUM   4  // 热电偶通道数量
#define THERMO_CHANNEL_START 8  // 热电偶起始通道

// 冷端补偿通道
#define COLD_COMP_CHANNEL   12  // 冷端补偿通道

// 其他模拟输入通道
#define OTHER_CHANNEL_NUM    3  // 其他模拟输入通道数量
#define OTHER_CHANNEL_START  13 // 其他模拟输入起始通道

// ARC分析仪温度通道定义
#define ARC_SAMPLE_TEMP_CHANNEL 0  // 样品温度通道(使用热电偶通道0)
#define ARC_CAVITY_TEMP_CHANNEL 1  // 腔体温度通道(使用热电偶通道1)

// ADC采样值缓冲区
extern u16 ADC_ConvertedValue[ADC_CHANNEL_NUM];

// 函数声明
void ADC_Init(void);                   // ADC初始化
float Get_Pressure(u8 channel);        // 获取压力值(MPa)
float Get_Temperature(u8 channel);     // 获取温度值(°C)
float Get_ColdCompensation(void);      // 获取冷端补偿值(°C)
float Get_AnalogInput(u8 channel);     // 获取其他模拟输入值(V)
void ADC_Start(void);                  // 启动ADC转换
void ADC_Stop(void);                   // 停止ADC转换

#endif 