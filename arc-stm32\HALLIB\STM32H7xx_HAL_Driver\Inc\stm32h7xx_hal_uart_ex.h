/**
  ******************************************************************************
  * @file    stm32h7xx_hal_uart_ex.h
  * <AUTHOR> Application Team
  * @brief   Header file of UART HAL Extended module.
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT(c) 2017 STMicroelectronics</center></h2>
  *
  * Redistribution and use in source and binary forms, with or without modification,
  * are permitted provided that the following conditions are met:
  *   1. Redistributions of source code must retain the above copyright notice,
  *      this list of conditions and the following disclaimer.
  *   2. Redistributions in binary form must reproduce the above copyright notice,
  *      this list of conditions and the following disclaimer in the documentation
  *      and/or other materials provided with the distribution.
  *   3. Neither the name of STMicroelectronics nor the names of its contributors
  *      may be used to endorse or promote products derived from this software
  *      without specific prior written permission.
  *
  * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
  * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
  * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
  * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
  * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
  * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
  * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
  * CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
  * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
  * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  *
  ******************************************************************************
  */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __STM32H7xx_HAL_UART_EX_H
#define __STM32H7xx_HAL_UART_EX_H

#ifdef __cplusplus
 extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32h7xx_hal_def.h"

/** @addtogroup STM32H7xx_HAL_Driver
  * @{
  */

/** @addtogroup UARTEx
  * @{
  */

/* Exported types ------------------------------------------------------------*/
/** @defgroup UARTEx_Exported_Types UARTEx Exported Types
  * @{
  */

/**
  * @brief  UART wake up from stop mode parameters
  */
typedef struct
{
  uint32_t WakeUpEvent;        /*!< Specifies which event will activat the Wakeup from Stop mode flag (WUF).
                                    This parameter can be a value of @ref UART_WakeUp_from_Stop_Selection.
                                    If set to UART_WAKEUP_ON_ADDRESS, the two other fields below must
                                    be filled up. */

  uint16_t AddressLength;      /*!< Specifies whether the address is 4 or 7-bit long.
                                    This parameter can be a value of @ref UARTEx_WakeUp_Address_Length.  */

  uint8_t Address;             /*!< UART/USART node address (7-bit long max). */
} UART_WakeUpTypeDef;

/**
  * @}
  */

/* Exported constants --------------------------------------------------------*/
/** @defgroup UARTEx_Exported_Constants UARTEx Exported Constants
  * @{
  */

/** @defgroup UARTEx_Word_Length UART Word Length
  * @{
  */
#define UART_WORDLENGTH_7B                  ((uint32_t)USART_CR1_M1)   /*!< 7-bit long UART frame */
#define UART_WORDLENGTH_8B                  ((uint32_t)0x00000000U)    /*!< 8-bit long UART frame */
#define UART_WORDLENGTH_9B                  ((uint32_t)USART_CR1_M0)   /*!< 9-bit long UART frame */
/**
  * @}
  */

/** @defgroup UARTEx_WakeUp_Address_Length UART Extended WakeUp Address Length
  * @{
  */
#define UART_ADDRESS_DETECT_4B              ((uint32_t)0x00000000U)      /*!< 4-bit long wake-up address */
#define UART_ADDRESS_DETECT_7B              ((uint32_t)USART_CR2_ADDM7)  /*!< 7-bit long wake-up address */
/**
  * @}
  */

/**
  * @}
  */

/* Exported macros -----------------------------------------------------------*/
/* Exported functions --------------------------------------------------------*/
/** @addtogroup UARTEx_Exported_Functions
  * @{
  */

/** @addtogroup UARTEx_Exported_Functions_Group1
  * @{
  */

/* Initialization and de-initialization functions  ****************************/
HAL_StatusTypeDef HAL_RS485Ex_Init(UART_HandleTypeDef *huart, uint32_t Polarity, uint32_t AssertionTime, uint32_t DeassertionTime);

/**
  * @}
  */

/* IO operation functions *****************************************************/

/** @addtogroup UARTEx_Exported_Functions_Group3
  * @{
  */

/* Peripheral Control functions  **********************************************/
HAL_StatusTypeDef HAL_UARTEx_StopModeWakeUpSourceConfig(UART_HandleTypeDef *huart, UART_WakeUpTypeDef WakeUpSelection);
HAL_StatusTypeDef HAL_UARTEx_EnableStopMode(UART_HandleTypeDef *huart);
HAL_StatusTypeDef HAL_UARTEx_DisableStopMode(UART_HandleTypeDef *huart);
HAL_StatusTypeDef HAL_MultiProcessorEx_AddressLength_Set(UART_HandleTypeDef *huart, uint32_t AddressLength);
void HAL_UARTEx_WakeupCallback(UART_HandleTypeDef *huart);

/**
  * @}
  */

/**
  * @}
  */

/* Private macros ------------------------------------------------------------*/
/** @defgroup UARTEx_Private_Macros UARTEx Private Macros
  * @{
  */

/** @brief  Report the UART clock source.
  * @param  __HANDLE__: specifies the UART Handle.
  * @param  __CLOCKSOURCE__: output variable.
  * @retval UART clocking source, written in __CLOCKSOURCE__.
  */
#define UART_GETCLOCKSOURCE(__HANDLE__,__CLOCKSOURCE__)       \
  do {                                                        \
    if((__HANDLE__)->Instance == USART1)                      \
    {                                                         \
       switch(__HAL_RCC_GET_USART1_SOURCE())                  \
       {                                                      \
        case RCC_USART1CLKSOURCE_D2PCLK2:                     \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_D2PCLK2;       \
          break;                                              \
        case RCC_USART1CLKSOURCE_PLL2:                        \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_PLL2;          \
          break;                                              \
        case RCC_USART1CLKSOURCE_PLL3:                        \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_PLL3;          \
          break;                                              \
        case RCC_USART1CLKSOURCE_HSI:                         \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_HSI;           \
          break;                                              \
        case RCC_USART1CLKSOURCE_CSI:                         \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_CSI;           \
          break;                                              \
        case RCC_USART1CLKSOURCE_LSE:                         \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_LSE;           \
          break;                                              \
       }                                                      \
    }                                                         \
    else if((__HANDLE__)->Instance == USART2)                 \
    {                                                         \
       switch(__HAL_RCC_GET_USART2_SOURCE())                  \
       {                                                      \
        case RCC_USART2CLKSOURCE_D2PCLK1:                     \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_D2PCLK1;       \
          break;                                              \
        case RCC_USART2CLKSOURCE_PLL2:                        \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_PLL2;          \
          break;                                              \
        case RCC_USART2CLKSOURCE_PLL3:                        \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_PLL3;          \
          break;                                              \
        case RCC_USART2CLKSOURCE_HSI:                         \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_HSI;           \
          break;                                              \
        case RCC_USART2CLKSOURCE_CSI:                         \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_CSI;           \
          break;                                              \
        case RCC_USART2CLKSOURCE_LSE:                         \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_LSE;           \
          break;                                              \
       }                                                      \
    }                                                         \
    else if((__HANDLE__)->Instance == USART3)                 \
    {                                                         \
       switch(__HAL_RCC_GET_USART3_SOURCE())                  \
       {                                                      \
        case RCC_USART3CLKSOURCE_D2PCLK1:                     \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_D2PCLK1;       \
          break;                                              \
        case RCC_USART3CLKSOURCE_PLL2:                        \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_PLL2;          \
          break;                                              \
        case RCC_USART3CLKSOURCE_PLL3:                        \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_PLL3;          \
          break;                                              \
        case RCC_USART3CLKSOURCE_HSI:                         \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_HSI;           \
          break;                                              \
        case RCC_USART3CLKSOURCE_CSI:                         \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_CSI;           \
          break;                                              \
        case RCC_USART3CLKSOURCE_LSE:                         \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_LSE;           \
          break;                                              \
       }                                                      \
    }                                                         \
    else if((__HANDLE__)->Instance == UART4)                  \
    {                                                         \
       switch(__HAL_RCC_GET_UART4_SOURCE())                   \
       {                                                      \
        case RCC_UART4CLKSOURCE_D2PCLK1:                      \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_D2PCLK1;       \
          break;                                              \
        case RCC_UART4CLKSOURCE_PLL2:                         \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_PLL2;          \
          break;                                              \
        case RCC_UART4CLKSOURCE_PLL3:                         \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_PLL3;          \
          break;                                              \
        case RCC_UART4CLKSOURCE_HSI:                          \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_HSI;           \
          break;                                              \
        case RCC_UART4CLKSOURCE_CSI:                          \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_CSI;           \
          break;                                              \
        case RCC_UART4CLKSOURCE_LSE:                          \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_LSE;           \
          break;                                              \
       }                                                      \
    }                                                         \
    else if ((__HANDLE__)->Instance == UART5)                 \
    {                                                         \
       switch(__HAL_RCC_GET_UART5_SOURCE())                   \
       {                                                      \
        case RCC_UART5CLKSOURCE_D2PCLK1:                      \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_D2PCLK1;       \
          break;                                              \
        case RCC_UART5CLKSOURCE_PLL2:                         \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_PLL2;          \
          break;                                              \
        case RCC_UART5CLKSOURCE_PLL3:                         \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_PLL3;          \
          break;                                              \
        case RCC_UART5CLKSOURCE_HSI:                          \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_HSI;           \
          break;                                              \
        case RCC_UART5CLKSOURCE_CSI:                          \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_CSI;           \
          break;                                              \
        case RCC_UART5CLKSOURCE_LSE:                          \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_LSE;           \
          break;                                              \
       }                                                      \
    }                                                         \
    else if((__HANDLE__)->Instance == USART6)                 \
    {                                                         \
       switch(__HAL_RCC_GET_USART6_SOURCE())                  \
       {                                                      \
        case RCC_USART6CLKSOURCE_D2PCLK2:                     \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_D2PCLK2;       \
          break;                                              \
        case RCC_USART6CLKSOURCE_PLL2:                        \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_PLL2;          \
          break;                                              \
        case RCC_USART6CLKSOURCE_PLL3:                        \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_PLL3;          \
          break;                                              \
        case RCC_USART6CLKSOURCE_HSI:                         \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_HSI;           \
          break;                                              \
        case RCC_USART6CLKSOURCE_CSI:                         \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_CSI;           \
          break;                                              \
        case RCC_USART6CLKSOURCE_LSE:                         \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_LSE;           \
          break;                                              \
       }                                                      \
    }                                                         \
    else if((__HANDLE__)->Instance == UART7)                  \
    {                                                         \
       switch(__HAL_RCC_GET_UART7_SOURCE())                   \
       {                                                      \
        case RCC_UART7CLKSOURCE_D2PCLK1:                      \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_D2PCLK1;       \
          break;                                              \
        case RCC_UART7CLKSOURCE_PLL2:                         \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_PLL2;          \
          break;                                              \
        case RCC_UART7CLKSOURCE_PLL3:                         \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_PLL3;          \
          break;                                              \
        case RCC_UART7CLKSOURCE_HSI:                          \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_HSI;           \
          break;                                              \
        case RCC_UART7CLKSOURCE_CSI:                          \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_CSI;           \
          break;                                              \
        case RCC_UART7CLKSOURCE_LSE:                          \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_LSE;           \
          break;                                              \
       }                                                      \
    }                                                         \
    else if((__HANDLE__)->Instance == UART8)                  \
    {                                                         \
       switch(__HAL_RCC_GET_UART8_SOURCE())                   \
       {                                                      \
        case RCC_UART8CLKSOURCE_D2PCLK1:                      \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_D2PCLK1;       \
          break;                                              \
        case RCC_UART8CLKSOURCE_PLL2:                         \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_PLL2;          \
          break;                                              \
        case RCC_UART8CLKSOURCE_PLL3:                         \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_PLL3;          \
          break;                                              \
        case RCC_UART8CLKSOURCE_HSI:                          \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_HSI;           \
          break;                                              \
        case RCC_UART8CLKSOURCE_CSI:                          \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_CSI;           \
          break;                                              \
        case RCC_UART8CLKSOURCE_LSE:                          \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_LSE;           \
          break;                                              \
       }                                                      \
    }                                                         \
    else if((__HANDLE__)->Instance == LPUART1)                \
    {                                                         \
       switch(__HAL_RCC_GET_LPUART1_SOURCE())                 \
       {                                                      \
        case RCC_LPUART1CLKSOURCE_D3PCLK1:                    \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_D3PCLK1;       \
          break;                                              \
        case RCC_LPUART1CLKSOURCE_PLL2:                       \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_PLL2;          \
          break;                                              \
        case RCC_LPUART1CLKSOURCE_PLL3:                       \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_PLL3;          \
          break;                                              \
        case RCC_LPUART1CLKSOURCE_HSI:                        \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_HSI;           \
          break;                                              \
        case RCC_LPUART1CLKSOURCE_CSI:                        \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_CSI;           \
          break;                                              \
        case RCC_LPUART1CLKSOURCE_LSE:                        \
          (__CLOCKSOURCE__) = UART_CLOCKSOURCE_LSE;           \
          break;                                              \
       }                                                      \
    }                                                         \
  } while(0)

/** @brief  Report the UART mask to apply to retrieve the received data
  *         according to the word length and to the parity bits activation.
  * @note   If PCE = 1, the parity bit is not included in the data extracted
  *         by the reception API().
  *         This masking operation is not carried out in the case of
  *         DMA transfers.
  * @param  __HANDLE__: specifies the UART Handle.
  * @retval None, the mask to apply to UART RDR register is stored in (__HANDLE__)->Mask field.
  */
#define UART_MASK_COMPUTATION(__HANDLE__)                             \
  do {                                                                \
  if ((__HANDLE__)->Init.WordLength == UART_WORDLENGTH_9B)            \
  {                                                                   \
     if ((__HANDLE__)->Init.Parity == UART_PARITY_NONE)               \
     {                                                                \
        (__HANDLE__)->Mask = 0x01FF ;                                 \
     }                                                                \
     else                                                             \
     {                                                                \
        (__HANDLE__)->Mask = 0x00FF ;                                 \
     }                                                                \
  }                                                                   \
  else if ((__HANDLE__)->Init.WordLength == UART_WORDLENGTH_8B)       \
  {                                                                   \
     if ((__HANDLE__)->Init.Parity == UART_PARITY_NONE)               \
     {                                                                \
        (__HANDLE__)->Mask = 0x00FF ;                                 \
     }                                                                \
     else                                                             \
     {                                                                \
        (__HANDLE__)->Mask = 0x007F ;                                 \
     }                                                                \
  }                                                                   \
  else if ((__HANDLE__)->Init.WordLength == UART_WORDLENGTH_7B)       \
  {                                                                   \
     if ((__HANDLE__)->Init.Parity == UART_PARITY_NONE)               \
     {                                                                \
        (__HANDLE__)->Mask = 0x007F ;                                 \
     }                                                                \
     else                                                             \
     {                                                                \
        (__HANDLE__)->Mask = 0x003F ;                                 \
     }                                                                \
  }                                                                   \
} while(0)


/**
  * @brief Ensure that UART frame length is valid.
  * @param __LENGTH__: UART frame length.
  * @retval SET (__LENGTH__ is valid) or RESET (__LENGTH__ is invalid)
  */
#define IS_UART_WORD_LENGTH(__LENGTH__) (((__LENGTH__) == UART_WORDLENGTH_7B) || \
                                         ((__LENGTH__) == UART_WORDLENGTH_8B) || \
                                         ((__LENGTH__) == UART_WORDLENGTH_9B))

/**
  * @brief Ensure that UART wake-up address length is valid.
  * @param __ADDRESS__: UART wake-up address length.
  * @retval SET (__ADDRESS__ is valid) or RESET (__ADDRESS__ is invalid)
  */
#define IS_UART_ADDRESSLENGTH_DETECT(__ADDRESS__) (((__ADDRESS__) == UART_ADDRESS_DETECT_4B) || \
                                                   ((__ADDRESS__) == UART_ADDRESS_DETECT_7B))

/**
  * @}
  */

/* Private functions ---------------------------------------------------------*/
/* Private constants ---------------------------------------------------------*/
/**
  * @}
  */

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif /* __STM32H7xx_HAL_UART_EX_H */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
