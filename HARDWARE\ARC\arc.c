#include "arc.h"
#include "delay.h"
#include "usart.h"
#include "stdio.h"  // ����stdio.hͷ�ļ�
#include "string.h" // ����string.hͷ�ļ�

// ȫ�ֱ���
ArcState_TypeDef ArcState = ARC_STATE_IDLE;
static float SampleTemp = 0.0f;         // ��Ʒ�¶�
static float CavityTemp = 0.0f;         // ǻ���¶�
static float TempDiff = 0.0f;           // �¶Ȳ�ֵ
static float SampleTempRate = 0.0f;     // ��Ʒ�¶ȱ仯����
static u32 LastSampleTime = 0;          // �ϴβ���ʱ��
static u32 LastRateCalcTime = 0;        // �ϴ����ʼ���ʱ��
static float PreviousSampleTemp = 0.0f; // �ϴ���Ʒ�¶�
static u8 CavityHeatingState = 0;       // ǻ�����״̬
static float TempDiffPreset = TEMP_DIFF_PRESET;        // �¶Ȳ�ֵԤ��ֵ
static float TempRateThreshold = TEMP_RATE_THRESHOLD;  // �¶ȱ仯������ֵ

// �����ϱ���ر���
static u32 ExperimentStartTime = 0;     // ʵ�鿪ʼʱ��
static u32 LastReportTime = 0;          // �ϴ������ϱ�ʱ��

/**
 * @brief ARCϵͳ��ʼ��
 * @param None
 * @retval None
 */
void ARC_Init(void)
{
    // ��ʼ��ADC
    ADC_Init();
    
    // ��ʼ���̵���
    RELAY_Init();
    
    // ��ʼ״̬�ر����м���
    ARC_ControlCavityHeating(0);
    RELAY_Set(SAMPLE_HEATER, 0);
    
    // ��ʼ��ϵͳ״̬
    ArcState = ARC_STATE_IDLE;
    LastSampleTime = HAL_GetTick();
    LastRateCalcTime = HAL_GetTick();
    LastReportTime = HAL_GetTick();
    PreviousSampleTemp = ARC_GetSampleTemp();
    
    printf("ARC���ȷ����ǳ�ʼ�����\r\n");
    printf("Ԥ�����: �¶Ȳ�ֵ=%.1f��C, �¶ȱ仯��ֵ=%.3f��C/min\r\n", 
           TempDiffPreset, TempRateThreshold);
}

/**
 * @brief ARCϵͳ������������Ӧ����ѭ���е���
 * @param None
 * @retval None
 */
void ARC_Process(void)
{
    u32 currentTime = HAL_GetTick();
    
    // ��ʱ�ɼ��¶�
    if (currentTime - LastSampleTime >= SAMPLE_INTERVAL)
    {
        LastSampleTime = currentTime;
        
        // ��ȡ��ǰ�¶�
        SampleTemp = ARC_GetSampleTemp();
        CavityTemp = ARC_GetCavityTemp();
        TempDiff = CavityTemp - SampleTemp;
        
        // �����¶ȱ仯����
        if (currentTime - LastRateCalcTime >= RATE_CALC_PERIOD)
        {
            SampleTempRate = ARC_CalcTempRate(SampleTemp);
            LastRateCalcTime = currentTime;
            PreviousSampleTemp = SampleTemp;
            
            // ����Ƿ�ﵽʵ���������
            if (SampleTempRate <= TempRateThreshold && ArcState != ARC_STATE_IDLE)
            {
                ArcState = ARC_STATE_COMPLETED;
                ARC_ControlCavityHeating(0); // �ر�ǻ�����
                RELAY_Set(SAMPLE_HEATER, 0); // �ر���Ʒ����
                printf("ʵ�����: ��Ʒ�¶ȱ仯���� = %.4f ��C/min\r\n", SampleTempRate);
            }
        }
        
        // ����ϵͳ״̬
        ARC_UpdateState();
        
        // �������
        printf("��Ʒ�¶�: %.2f��C, ǻ���¶�: %.2f��C, ��ֵ: %.2f��C, �仯��: %.4f��C/min\r\n", 
               SampleTemp, CavityTemp, TempDiff, SampleTempRate);
    }
    
    // �����ϱ�
    ARC_ReportData();
}

/**
 * @brief ��ȡ��Ʒ�¶�
 * @param None
 * @retval ��Ʒ�¶�(��C)
 */
float ARC_GetSampleTemp(void)
{
    // ʹ��ADCģ���ȡ��Ʒ�¶�
    return Get_Temperature(SAMPLE_TEMP_CHANNEL);
}

/**
 * @brief ��ȡǻ���¶�
 * @param None
 * @retval ǻ���¶�(��C)
 */
float ARC_GetCavityTemp(void)
{
    // ʹ��ADCģ���ȡǻ���¶�
    return Get_Temperature(CAVITY_TEMP_CHANNEL);
}

/**
 * @brief ����ǻ�����
 * @param state: 0-�ر����м��ȣ�1-��������
 * @retval None
 */
void ARC_ControlCavityHeating(u8 state)
{
    CavityHeatingState = state;
    
    if (state)
    {
        // ��������ǻ����ȼ̵���
        RELAY_Set(CAVITY_HEATER1, 1);
        RELAY_Set(CAVITY_HEATER2, 1);
        RELAY_Set(CAVITY_HEATER3, 1);
        RELAY_Set(CAVITY_HEATER4, 1);
    }
    else
    {
        // �ر�����ǻ����ȼ̵���
        RELAY_Set(CAVITY_HEATER1, 0);
        RELAY_Set(CAVITY_HEATER2, 0);
        RELAY_Set(CAVITY_HEATER3, 0);
        RELAY_Set(CAVITY_HEATER4, 0);
    }
}

/**
 * @brief �����¶ȱ仯����
 * @param current_temp: ��ǰ�¶�
 * @retval �¶ȱ仯����(��C/min)
 */
float ARC_CalcTempRate(float current_temp)
{
    float deltaTemp = current_temp - PreviousSampleTemp;
    float deltaTime = (float)(RATE_CALC_PERIOD) / 60000.0f; // ת��Ϊ����
    
    return deltaTemp / deltaTime;
}

/**
 * @brief ����ϵͳ״̬
 * @param None
 * @retval None
 */
void ARC_UpdateState(void)
{
    switch (ArcState)
    {
        case ARC_STATE_IDLE:
            // ����״̬����ִ�в���
            break;
            
        case ARC_STATE_HEATING:
            // ���ǻ���¶�����Ʒ�¶Ȳ�ֵ�ﵽԤ��ֵ��ֹͣǻ�����
            if (TempDiff >= TempDiffPreset)
            {
                ArcState = ARC_STATE_COOLING;
                ARC_ControlCavityHeating(0); // ֹͣǻ�����
                printf("ǻ���¶ȴﵽԤ���ֵ��ֹͣǻ�����\r\n");
            }
            break;
            
        case ARC_STATE_COOLING:
            // ���ǻ���¶�����Ʒ�¶���ȣ����¿�ʼǻ�����
            if (TempDiff <= 0.1f)
            {
                ArcState = ARC_STATE_HEATING;
                ARC_ControlCavityHeating(1); // ���¿�ʼǻ�����
                printf("ǻ���¶�����Ʒ�¶Ƚӽ������¿�ʼǻ�����\r\n");
            }
            break;
            
        case ARC_STATE_COMPLETED:
            // ʵ������ɣ��������
            break;
            
        default:
            break;
    }
}

/**
 * @brief ��ʼʵ��
 * @param None
 * @retval None
 */
void ARC_Start(void)
{
    if (ArcState == ARC_STATE_IDLE || ArcState == ARC_STATE_COMPLETED)
    {
        // ������Ʒ����
        RELAY_Set(SAMPLE_HEATER, 1);
        
        // ����ǻ�����
        ARC_ControlCavityHeating(1);
        
        // ����״̬
        ArcState = ARC_STATE_HEATING;
        
        // ��ʼ��ʱ����¶�
        LastSampleTime = HAL_GetTick();
        LastRateCalcTime = HAL_GetTick();
        LastReportTime = HAL_GetTick();
        ExperimentStartTime = HAL_GetTick();  // ��¼ʵ�鿪ʼʱ��
        PreviousSampleTemp = ARC_GetSampleTemp();
        
        printf("��ʼʵ��: ͬ������ǻ�����Ʒ\r\n");
    }
}

/**
 * @brief ֹͣʵ��
 * @param None
 * @retval None
 */
void ARC_Stop(void)
{
    // �ر����м���
    ARC_ControlCavityHeating(0);
    RELAY_Set(SAMPLE_HEATER, 0);
    
    // ����״̬
    ArcState = ARC_STATE_IDLE;
    
    printf("ʵ����ֹͣ\r\n");
}

/**
 * @brief �����¶Ȳ�ֵ
 * @param diff: �¶Ȳ�ֵ(��C)
 * @retval None
 */
void ARC_SetTempDiff(float diff)
{
    if (diff > 0.0f && diff < 50.0f)
    {
        TempDiffPreset = diff;
        printf("TempDiffPreset: %.1f��C\r\n", TempDiffPreset);
    }
}

/**
 * @brief ����������ֵ
 * @param rate: ������ֵ(��C/min)
 * @retval None
 */
void ARC_SetRateThreshold(float rate)
{
    if (rate > 0.0f && rate < 1.0f)
    {
        TempRateThreshold = rate;
        printf("�¶ȱ仯������ֵ������Ϊ: %.3f��C/min\r\n", TempRateThreshold);
    }
}

/**
 * @brief �����ϱ�����
 * @param None
 * @retval None
 */
void ARC_ReportData(void)
{
    u32 currentTime = HAL_GetTick();
    float runTime = 0.0f;
    uint8_t stateCode;
    char dataFrame[DATA_FRAME_MAX_LEN];
    uint8_t checksum;
    
    // ����Ƿ񵽴��ϱ����
    if (currentTime - LastReportTime >= DATA_REPORT_INTERVAL)
    {
        LastReportTime = currentTime;
        
        // ����ʵ��������ʱ��(��)
        if (ExperimentStartTime > 0)
        {
            runTime = (float)(currentTime - ExperimentStartTime) / 1000.0f;
        }
        
        // ��ȡϵͳ״̬����
        switch (ArcState)
        {
            case ARC_STATE_IDLE:
                stateCode = SYS_STATE_IDLE;
                break;
            case ARC_STATE_HEATING:
                stateCode = SYS_STATE_HEATING;
                break;
            case ARC_STATE_COOLING:
                stateCode = SYS_STATE_COOLING;
                break;
            case ARC_STATE_COMPLETED:
                stateCode = SYS_STATE_COMPLETED;
                break;
            default:
                stateCode = SYS_STATE_IDLE;
                break;
        }
        
        // ��������֡
        sprintf(dataFrame, "$ARC,%.1f,%.2f,%.2f,%.2f,%.4f,%d,", 
                runTime, SampleTemp, CavityTemp, TempDiff, SampleTempRate, stateCode);
        
        // ����У���
        checksum = ARC_CalculateChecksum(dataFrame + 1, strlen(dataFrame) - 1);
        
        // ����У��Ͳ���������
        sprintf(dataFrame + strlen(dataFrame), "%02X*\r\n", checksum);
        
        // ͨ�����ڷ�������
        HAL_UART_Transmit(&UART1_Handler, (uint8_t*)dataFrame, strlen(dataFrame), 100);
    }
}

/**
 * @brief ����У���(���У��)
 * @param str: Ҫ����У��͵��ַ���
 * @param len: �ַ�������
 * @retval У��ͽ��
 */
uint8_t ARC_CalculateChecksum(char *str, uint16_t len)
{
    uint8_t checksum = 0;
    uint16_t i;
    
    for (i = 0; i < len; i++)
    {
        checksum ^= str[i];
    }
    
    return checksum;
} 