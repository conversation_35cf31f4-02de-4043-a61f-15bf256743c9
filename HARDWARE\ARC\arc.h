#ifndef _ARC_H
#define _ARC_H
#include "sys.h"
#include "adc.h"
#include "relay.h"

// 温度控制参数
#define TEMP_DIFF_PRESET    5.0f        // 腔体与样品温度预设差值 (°C)
#define TEMP_RATE_THRESHOLD 0.02f       // 样品温度变化速率阈值 (°C/min)
#define SAMPLE_INTERVAL     1000        // 温度采样间隔 (ms)
#define RATE_CALC_PERIOD    60000       // 温度变化速率计算周期 (ms)

// 数据上报相关定义
#define DATA_REPORT_INTERVAL 1000       // 数据上报间隔(ms)
#define DATA_FRAME_MAX_LEN   80         // 数据帧最大长度

// 系统状态编码
#define SYS_STATE_IDLE       0          // 空闲状态
#define SYS_STATE_HEATING    1          // 加热状态
#define SYS_STATE_COOLING    2          // 冷却状态
#define SYS_STATE_COMPLETED  3          // 完成状态

// 系统状态定义
typedef enum {
    ARC_STATE_IDLE = 0,      // 空闲状态
    ARC_STATE_HEATING,       // 加热状态
    ARC_STATE_COOLING,       // 冷却状态
    ARC_STATE_COMPLETED      // 实验完成
} ArcState_TypeDef;

// 继电器通道定义
#define CAVITY_HEATER1      1           // 腔体加热器1
#define CAVITY_HEATER2      2           // 腔体加热器2
#define CAVITY_HEATER3      3           // 腔体加热器3
#define CAVITY_HEATER4      4           // 腔体加热器4
#define SAMPLE_HEATER       5           // 样品加热器

// 温度通道定义
#define SAMPLE_TEMP_CHANNEL 0           // 样品温度通道
#define CAVITY_TEMP_CHANNEL 1           // 腔体温度通道

// 外部变量
extern ArcState_TypeDef ArcState;       // ARC系统状态

// 函数声明
void ARC_Init(void);                    // ARC系统初始化
void ARC_Process(void);                 // ARC系统主处理函数
float ARC_GetSampleTemp(void);          // 获取样品温度
float ARC_GetCavityTemp(void);          // 获取腔体温度
void ARC_ControlCavityHeating(u8 state);// 控制腔体加热
float ARC_CalcTempRate(float current_temp); // 计算温度变化速率
void ARC_UpdateState(void);             // 更新系统状态
void ARC_Start(void);                   // 开始实验
void ARC_Stop(void);                    // 停止实验
void ARC_SetTempDiff(float diff);       // 设置温度差值
void ARC_SetRateThreshold(float rate);  // 设置速率阈值
void ARC_ReportData(void);              // 数据上报函数
uint8_t ARC_CalculateChecksum(char *str, uint16_t len); // 计算校验和

#endif 